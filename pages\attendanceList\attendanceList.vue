<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="考勤管理" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="scroll">
      <view class="repaire-list">
        <view class="li" @click="detail(item)" v-for="(item, index) in list">
          <view class="li-top flex">
            <view class="left flex-bd">{{ item.CreateDateTime }}</view>
          </view>
          <view class="li-bottom">
            <view class="flex">
              <view class="flex-hd">应到人数</view>
              <view class="flex-bd">{{ item.ReachedNum }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">实到人数</view>
              <view class="flex-bd">{{ item.PresentNum }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">备注</view>
              <view class="flex-bd line-1">{{ item.Remark }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <view class="fixed-add" @click="$c.naviTo('../attendanceAdd/attendanceAdd')">
      <i class="iconfont icon-tianjia"></i>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
    };
  },
  onLoad() {
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  onShow() {
    uni.$on("refreshAttendanceList", () => {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
    });
  },
  onUnload() {
    uni.$off("refreshAttendanceList");
  },
  methods: {
    detail(item) {
      uni.navigateTo({ url: "../attendanceDetail/attendanceDetail?Code=" + item.Code });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAttendanceList(
            {
              CreateDateTime: "", //日期
              CreateUserCode: uni.getStorageSync("UserCode"),
              PageIndex: page,
              PageSize: 10,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            for (let index in res.data) {
              res.data[index].CreateDateTime = res.data[index].CreateDateTime.replace("T", " ");
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
