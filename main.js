import App from "./App";
import uView from "@/uni_modules/uview-ui";
import common from "./common/common.js";
import http from "./common/http/index.js";
import apis from "./common/apis.js";
// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";
Vue.config.productionTip = false;
App.mpType = "app";
const app = new Vue({
  ...App,
});

let onFun = uni.$on;
uni.$on = (eventName, obj) => {
  try {
    uni.$off(eventName);
  } catch (error) { }
  onFun(eventName, obj);
};

Vue.use(uView);

uni.$u.setConfig({
  // 修改$u.props对象的属性
  props: {
    // 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
    button: {
      throttleTime: 300
    }
    // 其他组件属性配置
    // ......
  }
})

Vue.prototype.$c = common;
Vue.prototype.$http = http;
Vue.prototype.$apis = apis;
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";
export function createApp() {
  const app = createSSRApp(App);
  return {
    app,
  };
}
// #endif
import MyUpload from "./components/my-upload/my-upload.vue";
Vue.component("my-upload", MyUpload);
