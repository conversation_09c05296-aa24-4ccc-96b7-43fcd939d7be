<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="能耗登记" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">登记信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 抄表日期 -->
          <u-form-item label="抄表日期" borderBottom>
            <u-input v-model="form.ReadingTime" disabledColor="#fff" disabled border="none" placeholder="请输入抄表日期" />
          </u-form-item>
          <!-- 水电表类型 -->
          <u-form-item label="水电表类型" borderBottom>
            <u-input v-model="form.MeterType" disabledColor="#fff" disabled border="none" placeholder="请输入类型(水/电)" />
          </u-form-item>
          <!-- 表号 -->
          <u-form-item label="表号" borderBottom>
            <u-input v-model="form.MeterNumber" disabledColor="#fff" disabled border="none" placeholder="请输入表号" />
          </u-form-item>

          <!-- 表数值 -->
          <u-form-item required label="表数值" borderBottom>
            <u-input v-model="form.MeterValue" border="none" type="number" placeholder="请输入表数值" />
          </u-form-item>
          <!-- 备注 -->
          <u-form-item label="备注" borderBottom>
            <u-textarea v-model="form.Remark" border="none" placeholder="请输入备注信息" />
          </u-form-item>
          <!-- 图片上传 -->
          <u-form-item label="图片">
            <my-upload v-model="fileList" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showDatePicker: false,
      form: {
        MeterCode: "",
        MeterNumber: "",
        MeterType: "",
        MeterValue: "",
        ReadingTime: "",
        Remark: "",
        Images: "",
        ReadingUserCode: uni.getStorageSync("UserInfo").Code || "",
        SignImage: "",
        IsOver: 0,
        LatLon: "",
      },
      fileList: [],
    };
  },
  onLoad(options) {
    this.$apis.getEnergyDetail({ Code: options.Code }).then((res) => {
      this.form.MeterCode = res.data.Code;
      this.form.MeterNumber = res.data.MeterNumber;
      this.form.MeterType = res.data.MeterType;
      this.form.ReadingTime = this.$u.timeFormat(new Date(), "yyyy-mm-dd hh:MM:ss");
    });

    uni.getLocation({
      type: "wgs84",
      success: (res) => {
        this.form.LatLon = res.latitude + "," + res.longitude;
      },
    });
  },
  methods: {
    handleUploadChange(data) {
      this.form.Images = data.urls;
    },
    submit() {
      if (!this.form.ReadingTime) {
        this.$u.toast("请选择用电日期");
        return false;
      }
      if (!this.form.MeterValue) {
        this.$u.toast("请填写表数值");
        return false;
      }
      this.$apis
        .addEnergyRecord(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);

            uni.$emit("refreshEnergyList", true);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
