<template>
  <view>
    <view class="index-head" :style="'padding-top:' + barhei + 'px;'">
      <view class="title line-1">{{ UserInfo.ProjectCodes && UserInfo.IsCheck == 2 ? UserInfo.ProjectCodesCaption : "欢迎使用物业管理平台" }}</view>
      <view class="subtitle flex">
        <view class="flex-bd">您好，{{ UserInfo.UserName }}</view>
        <!-- <view class="select-btn" v-if="hasProjectSelect" @click="$c.naviTo('../yzSelectProject/yzSelectProject')">切换项目</view> -->
      </view>
    </view>
    <view class="index-message">
      <view class="index-title flex bor-b" @click="$c.naviTo('../noticeList/noticeList?ProjectCode=' + projectCode)">
        <view class="flex-bd">通知公告</view>
        <view class="more">查看全部通知</view>
      </view>
      <view class="con flex">
        <view class="icon">
          <i class="iconfont icon-labamoren"></i>
        </view>
        <view class="flex-bd" v-if="notice.Title" @click="$c.naviTo('../noticeDetail/noticeDetail?Code=' + notice.Code)">
          <view class="text flex">
            <view class="flex-bd">{{ notice.Title }}</view>
            <view class="time">{{ notice.CreateDateTimeCaption }}</view>
          </view>
          <view class="desc">{{ notice.NoticeContent }}</view>
        </view>
        <view class="flex-bd" v-else>
          <view class="text flex">
            <view class="flex-bd">暂无未读通知</view>
          </view>
        </view>
      </view>
    </view>
    <view class="index-notice flex" @click="$c.naviTo('../payNoticeList/payNoticeList')">
      <view class="flex-hd">
        <text class="name">缴费</text>
        通知
      </view>
      <view class="flex-bd line-1">{{ paymentNotice.Title }}</view>
      <view>
        <u-icon name="arrow-right" size="14" color="#999"></u-icon>
      </view>
    </view>
    <view class="index-pannel">
      <view class="index-title flex bor-b">
        <view class="flex-bd">常用功能</view>
      </view>
      <view class="lis">
        <view class="li" v-if="checkService('设备报修')" @click="$c.naviTo('../repaireAdd/repaireAdd')">
          <view class="icon icon1">
            <i class="iconfont icon-weixiu"></i>
          </view>
          <view class="text">报事报修</view>
        </view>
        <view class="li" v-if="checkService('会议预约')" @click="$c.naviTo('../meetAdd/meetAdd')">
          <view class="icon icon2">
            <i class="iconfont icon-huiyishi"></i>
          </view>
          <view class="text">会议室预约</view>
        </view>
        <view class="li" v-if="checkService('卤菜外卖')" @click="$c.naviTo('../takeoutAdd/takeoutAdd')">
          <view class="icon icon3">
            <i class="iconfont icon-waimai"></i>
          </view>
          <view class="text">卤菜外卖</view>
        </view>
        <view class="li" v-if="checkService('餐食预留')" @click="$c.naviTo('../reserveAdd/reserveAdd')">
          <view class="icon icon4">
            <i class="iconfont icon-canshi"></i>
          </view>
          <view class="text">餐食预留</view>
        </view>
        <view class="li" v-if="checkService('包厢预定')" @click="$c.naviTo('../boxAdd/boxAdd')">
          <view class="icon icon5">
            <i class="iconfont icon-baoxiang"></i>
          </view>
          <view class="text">包厢预定</view>
        </view>
      </view>
    </view>
    <view class="index-section">
      <u-subsection :list="tabs" :current="0" :activeColor="$c.color()" fontSize="14" @change="sectionChange"></u-subsection>
    </view>
    <view class="repaire-list">
      <view class="li flex" @click="detail(item)" v-for="(item, index) in list">
        <block v-if="sectionIndex == 0">
          <view class="icon icon1" v-if="item.ModuleName == '包厢预定'">
            <i class="iconfont icon-baoxiang"></i>
          </view>
          <view class="icon icon2" v-if="item.ModuleName == '会议预约'">
            <i class="iconfont icon-huiyishi"></i>
          </view>
          <view class="icon icon3" v-if="item.ModuleName == '卤菜外卖'">
            <i class="iconfont icon-waimai"></i>
          </view>
          <view class="icon icon4" v-if="item.ModuleName == '餐食预留'">
            <i class="iconfont icon-canshi"></i>
          </view>
          <view class="flex-bd">
            <view class="li-top flex">
              <view class="left flex-bd">{{ item.ModuleName }}:{{ item.MeetingOrderNo }}</view>
              <view class="status status1" v-if="item.ApplyStatus == 0">待审核</view>
              <view class="status status2" v-else-if="item.ApplyStatus == 1">审核通过</view>
              <view class="status status3" v-else-if="item.ApplyStatus == 2">审核不通过</view>
              <view class="status status2" v-else-if="item.ApplyStatus == 3">{{ item.ModuleName == "会议预约" ? "进行中" : "已完成" }}</view>
              <view class="status status2" v-else-if="item.ApplyStatus == 4">{{ item.ModuleName == "会议预约" ? "已完成" : "已评价" }}</view>
              <view class="status status2" v-else-if="item.ApplyStatus == 5">已评价</view>
              <view class="status status2" v-else-if="item.ApplyStatus == -1">已取消</view>
            </view>
            <view class="li-bottom">
              <view class="flex">
                <view class="flex-hd">预约时间</view>
                <view class="flex-bd">{{ $c.formatDate(item.ApplyFromTime) }}</view>
              </view>
            </view>
          </view>
        </block>
        <block v-if="sectionIndex == 1">
          <view class="icon icon3">
            <i class="iconfont icon-weixiu"></i>
          </view>
          <view class="flex-bd">
            <view class="li-top flex">
              <view class="left flex-bd">报修单号:{{ item.RepairOrderNo }}</view>
              <view class="status status1" v-if="item.RepairStatus == 0">{{ item.RepairStatusCaption }}</view>
              <view class="status status2" v-else>{{ item.RepairStatusCaption }}</view>
            </view>
            <view class="li-bottom">
              <view class="flex">
                <view class="flex-hd">报修时间</view>
                <view class="flex-bd">{{ item.ApplyTimeCaption }}</view>
              </view>
              <view class="flex">
                <view class="flex-hd">报修内容</view>
                <view class="flex-bd line-1">{{ item.RepairMatter }}</view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
    <view v-if="list && list.length == 0">
      <view class="empty">
        <image src="@/static/images/nores.png"></image>
        <view class="text">暂无内容</view>
      </view>
    </view>
    <view class="index-more-btn tc">
      <view class="inline">
        <u-button type="info" v-if="sectionIndex == 0" @click="$c.naviTo('../orderList/orderList')">查看更多</u-button>
        <u-button type="info" v-if="sectionIndex == 1" @click="$c.naviTo('../repaireList/repaireList?ProjectCode=' + UserInfo.ProjectCodes)">查看更多</u-button>
      </view>
    </view>
    <view style="height: 30rpx"></view>
    <!-- 提示认证弹出框 -->
    <u-modal :show="rzShow" :showConfirmButton="false">
      <view class="rz-content">
        <image src="/static/images/icon-rz.png" mode="widthFix" class="rz-img"></image>
        <view class="rz-title">请先进行认证</view>
        <view class="rz-btns">
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="info" :customStyle="'height:80rpx;'" @click="logout">退出登录</u-button>
          </view>
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle() + 'height:80rpx;'" @click="$c.naviTo('../auth/auth')">确定</u-button>
          </view>
        </view>
      </view>
    </u-modal>
    <!-- 认证中弹出框 -->
    <u-modal :show="rzIngShow" :showConfirmButton="false">
      <view class="rz-content">
        <image src="/static/images/icon-rz.png" mode="widthFix" class="rz-img"></image>
        <view class="rz-title">认证中...</view>
        <view class="rz-btns">
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="info" :customStyle="'height:80rpx;'" @click="logout">退出登录</u-button>
          </view>
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle() + 'height:80rpx;'" @click="$c.naviTo('../auth/auth')">确定</u-button>
          </view>
        </view>
      </view>
    </u-modal>
    <!-- 添加u-tabbar -->
    <u-tabbar :value="current" :fixed="true" @change="tabbarChange" :activeColor="$c.color()" :placeholder="true" :safeAreaInsetBottom="true">
      <u-tabbar-item text="首页" icon="home"></u-tabbar-item>
      <u-tabbar-item text="我的" icon="account"></u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: "Hello",
      barhei: "",
      user: "",
      current: 0, // 当前选中的tabbar索引
      sectionIndex: 1,
      tabs: ["我的报修"],
      UserInfo: uni.getStorageSync("UserInfo"),
      list: [],
      rzShow: false,
      notice: {},
      projectCode: uni.getStorageSync("UserInfo").ProjectCodes,
      rzIngShow: false,
      hasProjectSelect: false,
      EnableServiceList: [],
      paymentNotice: {
        Title: "暂无未读通知",
      },
    };
  },
  onLoad() {
    uni.hideTabBar();
    var sys = uni.getSystemInfoSync();
    this.barhei = sys.statusBarHeight;

    if (!uni.getStorageSync("UserCode")) {
      uni.navigateTo({ url: "../login/login" }); //跳转到登录页
    }
    if (uni.getStorageSync("UserCode") && uni.getStorageSync("UserInfo").IsCheck == 2) {
      this.$apis
        .getNoticeList({
          PageIndex: 1,
          PageSize: 1,
          UserCode: uni.getStorageSync("UserCode"), //用户编码
          IsSee: 0, //是否查看 0-否 1- 是
          ProjectCode: this.projectCode, //项目编码
        })
        .then((res) => {
          if (res.data && res.data[0]) {
            res.data[0].NoticeContent = this.$c.getText(res.data[0].NoticeContent);
            this.notice = res.data[0];
          }
        });

      this.$apis
        .getPaymentNotice({
          CreateUserCode: uni.getStorageSync("UserCode"), //登录人
          IsSee: 0, //0未读  1已读
          PageIndex: 1,
          PageSize: 1,
        })
        .then((res) => {
          if (res.data && res.data[0]) {
            this.paymentNotice = res.data[0];
          }
        });
    }
  },
  onShow() {
    this.UserInfo = uni.getStorageSync("UserInfo");

    if (this.sectionIndex == 0) {
      this.getMyOrderList();
    }

    if (this.sectionIndex == 1) {
      this.getRepairList();
    }

    this.$apis
      .getUserInfo(
        {
          CellPhone: this.UserInfo.CellPhone,
        },
        { loading: false }
      )
      .then((res) => {
        var UserInfo = res.data;
        var ProjectCodes = UserInfo.ProjectCodes;
        var ProjectCodesCaption = UserInfo.ProjectCodesCaption;
        if (UserInfo.UserType == 1) {
          uni.setStorageSync("ProjectCodes", ProjectCodes);
          uni.setStorageSync("ProjectCodesCaption", ProjectCodesCaption);
          UserInfo.ProjectCodes = ProjectCodes ? ProjectCodes.split(",")[0] : "";
          UserInfo.ProjectCodesCaption = ProjectCodesCaption ? ProjectCodesCaption.split(",")[0] : "";
        }
        if (UserInfo.ProjectCodes.length > 0) {
          this.hasProjectSelect = true;
        }
        this.UserInfo = UserInfo;
        uni.setStorageSync("UserInfo", UserInfo);
        //判断有没有认证;
        if (uni.getStorageSync("UserInfo").IsCheck == 0) {
          return (this.rzShow = true);
        }
        if (uni.getStorageSync("UserInfo").IsCheck == 1) {
          return (this.rzIngShow = true);
        }

        this.$apis.getProjectInfo({ Code: UserInfo.ProjectCodes }, { loading: false }).then((res) => {
          var EnableService = res.data.EnableService;
          var EnableServiceList = EnableService.split(",");
          this.EnableServiceList = EnableServiceList;
        });
      });
  },
  methods: {
    checkService(service) {
      if (this.EnableServiceList.indexOf(service) != -1) {
        return true;
      }
      return false;
    },
    detail(item) {
      if (this.sectionIndex == 0) {
        if (item.ModuleName == "会议预约") {
          uni.navigateTo({ url: "../meetDetail/meetDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
        } else if (item.ModuleName == "卤菜外卖") {
          uni.navigateTo({ url: "../takeoutDetail/takeoutDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
        } else if (item.ModuleName == "餐食预留") {
          uni.navigateTo({ url: "../reserveDetail/reserveDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
        } else if (item.ModuleName == "包厢预定") {
          uni.navigateTo({ url: "../boxDetail/boxDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
        }
      } else {
        uni.navigateTo({ url: "../repaireDetail/repaireDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
      }
    },
    getMyOrderList() {
      this.$apis
        .getMyOrderList({
          ProjectCode: this.projectCode,
          RepairStatusSearch: "", //预约成功、预约失败、待审核、已完成
          ApplyUserCode: uni.getStorageSync("UserCode"),
          RepairOrderNo: "",
          PageIndex: 1,
          PageSize: 5,
        })
        .then((res) => {
          this.list = res.data;
        });
    },
    getRepairList() {
      this.$apis
        .getMyRepairList({
          ProjectCode: this.projectCode,
          RepairStatusSearch: "",
          ApplyUserCode: uni.getStorageSync("UserCode"),
          RepairOrderNo: "",
          PageIndex: 1,
          PageSize: 5,
        })
        .then((res) => {
          this.list = res.data;
        });
    },
    sectionChange(index) {
      if (this.sectionIndex != index) {
        this.list = [];
        this.sectionIndex = index;
        if (index == 0) {
          this.getMyOrderList();
        } else {
          this.getRepairList();
        }
      }
    },
    tabbarChange(index) {
      const tabList = ["../yzHome/yzHome", "../user/user"];
      uni.switchTab({
        url: tabList[index],
      });
    },
    logout() {
      uni.setStorageSync("UserInfo", "");
      uni.setStorageSync("UserCode", "");
      uni.setStorageSync("UserType", "");
      uni.navigateTo({
        url: "../login/login",
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
