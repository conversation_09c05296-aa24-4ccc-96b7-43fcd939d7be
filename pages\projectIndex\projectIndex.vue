<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="项目详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="project-info flex flext">
      <view class="flex-hd">
        <image :src="$c.getFullImage(info.MainImage)" mode="aspectFill"></image>
      </view>
      <view class="flex-bd">
        <view class="project-name">
          <view class="name">{{ info.ProjectName }}</view>
          <view class="des">
            <view>地址：{{ info.Address ? info.Address : "" }}</view>
            <view>项目负责人：{{ info.UserName ? info.UserName : "" }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="index-message" style="margin-top: 0">
      <view class="index-title flex bor-b" @click="$c.naviTo('../noticeList/noticeList?ProjectCode=' + Code)">
        <view class="flex-bd">通知公告</view>
        <view class="more">查看全部通知</view>
      </view>
      <view class="con flex">
        <view class="icon">
          <i class="iconfont icon-labamoren"></i>
        </view>
        <view class="flex-bd" v-if="notice.Title" @click="$c.naviTo('../noticeDetail/noticeDetail?Code=' + notice.Code)">
          <view class="text flex">
            <view class="flex-bd">{{ notice.Title }}</view>
            <view class="time">{{ notice.CreateDateTimeCaption }}</view>
          </view>
          <view class="desc">{{ notice.NoticeContent }}</view>
        </view>
        <view class="flex-bd" v-else>
          <view class="text flex">
            <view class="flex-bd">暂无未读通知</view>
          </view>
        </view>
      </view>
    </view>
    <view class="index-pannel">
      <view class="index-title flex bor-b">
        <view class="flex-bd">常用功能</view>
      </view>
      <view class="lis">
        <view class="li" v-if="checkService('设备报修')" @click="$c.naviTo('../repaireAdd/repaireAdd?ProjectCode=' + Code)">
          <view class="icon icon1">
            <i class="iconfont icon-weixiu"></i>
          </view>
          <view class="text">报事报修</view>
        </view>
        <view class="li" v-if="checkService('会议预约')" @click="$c.naviTo('../meetAdd/meetAdd?ProjectCode=' + Code)">
          <view class="icon icon2">
            <i class="iconfont icon-huiyishi"></i>
          </view>
          <view class="text">会议室预约</view>
        </view>
        <view class="li" v-if="checkService('卤菜外卖')" @click="$c.naviTo('../takeoutAdd/takeoutAdd?ProjectCode=' + Code)">
          <view class="icon icon3">
            <i class="iconfont icon-waimai"></i>
          </view>
          <view class="text">卤菜外卖</view>
        </view>
        <view class="li" v-if="checkService('餐食预留')" @click="$c.naviTo('../reserveAdd/reserveAdd?ProjectCode=' + Code)">
          <view class="icon icon4">
            <i class="iconfont icon-canshi"></i>
          </view>
          <view class="text">餐食预留</view>
        </view>
        <view class="li" v-if="checkService('包厢预定')" @click="$c.naviTo('../boxAdd/boxAdd?ProjectCode=' + Code)">
          <view class="icon icon5">
            <i class="iconfont icon-baoxiang"></i>
          </view>
          <view class="text">包厢预定</view>
        </view>
        <view class="li" v-if="checkService('设备报修')" @click="$c.naviTo('../repaireList/repaireList?ProjectCode=' + Code)">
          <view class="icon icon6">
            <i class="iconfont icon-weixiu2"></i>
          </view>
          <view class="text">我的报事报修</view>
        </view>
        <view class="li" v-if="checkService('会议预约')" @click="$c.naviTo('../orderList/orderList?ProjectCode=' + Code)">
          <view class="icon icon7">
            <i class="iconfont icon-huiyi"></i>
          </view>
          <view class="text">我的预约</view>
        </view>
        <view class="li" @click="$c.naviTo('../attendanceList/attendanceList?ProjectCode=' + Code)">
          <view class="icon icon7">
            <i class="iconfont icon-kaoqinguanli"></i>
          </view>
          <view class="text">考勤管理</view>
        </view>
      </view>
    </view>
    <view class="index-pannel">
      <view class="index-title flex bor-b">
        <view class="flex-bd">管理功能</view>
      </view>
      <view class="lis">
        <view class="li" v-if="checkMenu('专项检查')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=专项检查&ProjectCode=' + Code)">
          <view class="icon icon1">
            <i class="iconfont icon-anquanjiancha"></i>
            <view class="count" v-if="count.special > 0">{{ count.special }}</view>
          </view>
          <view class="text">专项检查</view>
        </view>
        <view class="li" v-if="checkMenu('绿化巡逻')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=绿化巡逻&ProjectCode=' + Code)">
          <view class="icon icon5">
            <i class="iconfont icon-mti-lvhua"></i>
            <view class="count" v-if="count.greening > 0">{{ count.greening }}</view>
          </view>
          <view class="text">绿化巡逻</view>
        </view>
        <view class="li" v-if="checkMenu('维保管理')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=维保管理&ProjectCode=' + Code)">
          <view class="icon icon2">
            <i class="iconfont icon-weibaojilu"></i>
            <view class="count" v-if="count.maintenance > 0">{{ count.maintenance }}</view>
          </view>
          <view class="text">维保管理</view>
        </view>
        <view class="li" v-if="checkMenu('食材验收')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=食材验收&ProjectCode=' + Code)">
          <view class="icon icon3">
            <i class="iconfont icon-shicai"></i>
            <view class="count" v-if="count.food > 0">{{ count.food }}</view>
          </view>
          <view class="text">食材验收</view>
        </view>
        <view class="li" v-if="checkMenu('设备巡检')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=设备巡检&ProjectCode=' + Code)">
          <view class="icon icon4">
            <i class="iconfont icon-shebei"></i>
            <view class="count" v-if="count.device > 0">{{ count.device }}</view>
          </view>
          <view class="text">设备巡检</view>
        </view>
        <view class="li" v-if="checkMenu('安防巡逻')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=安防巡逻&ProjectCode=' + Code)">
          <view class="icon icon3">
            <i class="iconfont icon-anfang"></i>
            <view class="count" v-if="count.patrol > 0">{{ count.patrol }}</view>
          </view>
          <view class="text">安防巡逻</view>
        </view>
        <view class="li" v-if="checkMenu('保洁监管')" @click="$c.naviTo('../inspectList/inspectList?ModuleName=保洁监管&ProjectCode=' + Code)">
          <view class="icon icon6">
            <i class="iconfont icon-baojie"></i>
            <view class="count" v-if="count.clean > 0">{{ count.clean }}</view>
          </view>
          <view class="text">保洁监管</view>
        </view>
        <!-- <view class="li" @click="$c.naviTo('../repaireAdd/repaireAdd')">
          <view class="icon icon7">
            <i class="iconfont icon-wenjianjia"></i>
          </view>
          <view class="text">档案管理</view>
        </view> -->
        <view class="li" v-if="checkMenu('能耗管理')" @click="$c.naviTo('../energyList/energyList?ProjectCode=' + Code)">
          <view class="icon icon1">
            <i class="iconfont icon-nenghao"></i>
            <view class="count" v-if="count.energy > 0">{{ count.energy }}</view>
          </view>
          <view class="text">能耗管理</view>
        </view>
        <view class="li" v-if="checkMenu('资产管理')" @click="$c.naviTo('../assetList/assetList?ProjectCode=' + Code)">
          <view class="icon icon2">
            <i class="iconfont icon-zichan"></i>
            <view class="count" v-if="count.asset > 0">{{ count.asset }}</view>
          </view>
          <view class="text">资产管理</view>
        </view>
        <view class="li" v-if="checkMenu('包厢预定') && checkService('包厢预定')" @click="$c.naviTo('../boxList/boxList?type=apply&ProjectCode=' + Code)">
          <view class="icon icon3">
            <i class="iconfont icon-baoxiang"></i>
            <view class="count" v-if="count.box > 0">{{ count.box }}</view>
          </view>
          <view class="text">包厢预定</view>
        </view>
        <view class="li" v-if="checkMenu('餐食预留') && checkService('餐食预留')" @click="$c.naviTo('../reserveList/reserveList?type=apply&ProjectCode=' + Code)">
          <view class="icon icon4">
            <i class="iconfont icon-canshi1"></i>
            <view class="count" v-if="count.reserve > 0">{{ count.reserve }}</view>
          </view>
          <view class="text">餐食预留</view>
        </view>
        <view class="li" v-if="checkMenu('卤菜外卖') && checkService('卤菜外卖')" @click="$c.naviTo('../takeoutList/takeoutList?type=apply&ProjectCode=' + Code)">
          <view class="icon icon5">
            <i class="iconfont icon-waimai1"></i>
            <view class="count" v-if="count.takeout > 0">{{ count.takeout }}</view>
          </view>
          <view class="text">卤菜外卖</view>
        </view>
        <view class="li" v-if="checkMenu('维修工单') && checkService('设备报修')" @click="$c.naviTo('../repaireList/repaireList?type=apply&ProjectCode=' + Code)">
          <view class="icon icon6">
            <i class="iconfont icon-weixiu2"></i>
            <view class="count" v-if="count.repair > 0">{{ count.repair }}</view>
          </view>
          <view class="text">工程工单</view>
        </view>
        <view class="li" v-if="checkMenu('会务预约') && checkService('会议预约')" @click="$c.naviTo('../meetList/meetList?type=apply&ProjectCode=' + Code)">
          <view class="icon icon7">
            <i class="iconfont icon-huiyi"></i>
            <view class="count" v-if="count.meet > 0">{{ count.meet }}</view>
          </view>
          <view class="text">会务预约</view>
        </view>
        <view class="li" v-if="checkMenu('工作日报')" @click="$c.naviTo('../dailyReportList/dailyReportList?type=apply&ProjectCode=' + Code)">
          <view class="icon icon1">
            <i class="iconfont icon-ribao"></i>
            <view class="count" v-if="count.daily > 0">{{ count.daily }}</view>
          </view>
          <view class="text">工作日报</view>
        </view>
      </view>
    </view>
    <view style="height: 1px"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: "",
      Code: "",
      menuList: [],
      count: {
        repair: 0,
        meet: 0,
        daily: 0,
        takeout: 0,
        box: 0,
        reserve: 0,
        asset: 0,
        energy: 0,
        security: 0,
        clean: 0,
        quality: 0,
        grid: 0,
        device: 0,
        patrol: 0,
      },
      notice: {},
      EnableServiceList: [],
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.$apis.getProjectInfo({ Code: options.Code }, { loading: false }).then((res) => {
      this.info = res.data;

      var EnableService = this.info.EnableService;
      if (EnableService.endsWith(",")) {
        EnableService = EnableService.slice(0, -1);
      }
      var EnableServiceList = EnableService.split(",");
      this.EnableServiceList = EnableServiceList;
      uni.setStorageSync("ProjectChargeUserCode", this.info.ProjectChargeUserCode);
    });

    if (!uni.getStorageSync("UserCode")) {
      return false;
    }

    this.$apis
      .getNoticeList({
        PageIndex: 1,
        PageSize: 1,
        UserCode: uni.getStorageSync("UserCode"), //用户编码
        IsSee: 0, //是否查看 0-否 1- 是
        ProjectCode: this.Code, //项目编码
      })
      .then((res) => {
        if (res.data && res.data[0]) {
          res.data[0].NoticeContent = this.$c.getText(res.data[0].NoticeContent);
          this.notice = res.data[0];
        }
      });

    this.$apis.getProjectMenu({ UserCode: uni.getStorageSync("UserCode") }, { loading: false }).then((res) => {
      console.log(res);
      this.menuList = res.data;

      if (res.data.indexOf("维修工单") != -1) {
        this.$apis.getRepairCount({ ProjectCode: this.Code, UserCode: uni.getStorageSync("UserCode") }, { loading: false }).then((res) => {
          this.count.repair = res.msg;
        });
      }
      if (res.data.indexOf("会务预约") != -1) {
        this.$apis.getMeetingCount({ ProjectCode: this.Code, UserCode: uni.getStorageSync("UserCode") }, { loading: false }).then((res) => {
          this.count.meet = res.msg;
        });
      }
      if (res.data.indexOf("工作日报") != -1) {
        this.$apis.getDailyCount({ ProjectCode: this.Code, UserCode: uni.getStorageSync("UserCode") }, { loading: false }).then((res) => {
          this.count.daily = res.msg;
        });
      }
      if (res.data.indexOf("卤菜外卖") != -1) {
        this.$apis.getTakeoutCount({ ProjectCode: this.Code }, { loading: false }).then((res) => {
          this.count.takeout = res.msg;
        });
      }
      if (res.data.indexOf("包厢预定") != -1) {
        this.$apis.getBoxCount({ ProjectCode: this.Code }, { loading: false }).then((res) => {
          this.count.box = res.msg;
        });
      }
      if (res.data.indexOf("餐食预留") != -1) {
        this.$apis.getReserveCount({ ProjectCode: this.Code }, { loading: false }).then((res) => {
          this.count.reserve = res.msg;
        });
      }

      if (res.data.indexOf("资产管理") != -1) {
        this.$apis.getAssetCount({ ProjectCode: this.Code }, { loading: false }).then((res) => {
          this.count.asset = res.msg;
        });
      }

      if (res.data.indexOf("能耗管理") != -1) {
        this.$apis.getEnergyCount({ ProjectCode: this.Code }, { loading: false }).then((res) => {
          this.count.energy = res.msg;
        });
      }

      if (res.data.indexOf("专项检查") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "专项检查" }, { loading: false }).then((res) => {
          this.count.special = res.msg;
        });
      }

      if (res.data.indexOf("绿化巡逻") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "绿化巡逻" }, { loading: false }).then((res) => {
          this.count.greening = res.msg;
        });
      }

      if (res.data.indexOf("维保管理") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "维保管理" }, { loading: false }).then((res) => {
          this.count.maintenance = res.msg;
        });
      }

      if (res.data.indexOf("食材验收") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "食材验收" }, { loading: false }).then((res) => {
          this.count.food = res.msg;
        });
      }

      if (res.data.indexOf("设备巡检") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "设备巡检" }, { loading: false }).then((res) => {
          this.count.device = res.msg;
        });
      }

      if (res.data.indexOf("安防巡逻") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "安防巡逻" }, { loading: false }).then((res) => {
          this.count.patrol = res.msg;
        });
      }

      if (res.data.indexOf("保洁监管") != -1) {
        this.$apis.getInspectCount({ ProjectCode: this.Code, ModuleName: "保洁监管" }, { loading: false }).then((res) => {
          this.count.clean = res.msg;
        });
      }
    });
  },
  methods: {
    checkMenu(menu) {
      if (this.menuList.indexOf(menu) != -1) {
        return true;
      }
      return false;
    },
    checkService(service) {
      if (this.EnableServiceList.indexOf(service) != -1) {
        return true;
      }
      return false;
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
