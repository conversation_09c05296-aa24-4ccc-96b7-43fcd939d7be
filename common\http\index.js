import Request from "@/common/luch-request/index.js";
const http = new Request();
var loading = 0;
var getTokenimg = false;
var requests = [];
var needToken = true;
let defaultUrl = "";
// let url = "https://kangyuan.anyoucloud.com";
var url = "";
if (process.env.NODE_ENV === "production") {
  //线上环境
  //url = "https://kangyuan.anyoucloud.com";
  url = "http://58.221.13.198:5141";
  defaultUrl = url + "/api";
} else {
  //开发环境
  url = "http://58.221.13.198:5141";
  defaultUrl = url + "/api";
}

// 添加刷新token的标记和等待队列
let isRefreshing = false;
let retryQueue = [];

// 执行队列中的请求
const executeQueue = (token) => {
  retryQueue.forEach((cb) => cb(token));
  retryQueue = [];
};

function getToken() {
  return new Promise((resolve) => {
    uni.request({
      url: url + "/api/Auth/Token",
      method: "GET",
      data: {
        AppId: "pms",
        AppSecret: "pms",
      },
      header: {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
      success: (res) => {
        resolve(res);
      },
    });
  });
}

// 给实例添加一个setToken方法，用于登录后方便将最新token动态添加到header，同时将token保存在localStorage中
http.setToken = (obj) => {
  http.config.header = {
    Authorization: "Bearer " + obj.token,
  };
  uni.setStorageSync("access_token", obj.token);
  uni.setStorageSync("token_time", new Date().getTime());
};

//设置全局配置
http.setConfig((config) => {
  config.baseURL = defaultUrl;
  config.staticURL = url;
  config.hostURL = url;
  config.custom = {
    loading: true,
  };
  return config;
});

//请求前
http.interceptors.request.use(
  (config) => {
    var a = JSON.parse(JSON.stringify(config));
    var data = a.data;
    console.log(a.url + "请求", data);
    if (config.custom.loading) {
      if (loading < 1) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      }
      loading++;
    }

    if (config.url.indexOf("/H5Token") >= 0) {
      return config;
    }
    if (needToken) {
      let now_time = new Date().getTime();
      let access_token = uni.getStorageSync("access_token");
      let token_time = uni.getStorageSync("token_time");
      if (!access_token || now_time > token_time + 3600 * 24 * 1000) {
        if (!getTokenimg) {
          getTokenimg = true;
          getToken()
            .then((res) => {
              console.log(res.data);
              const token = res.data;
              http.setToken({
                token,
              });
              getTokenimg = false;
              return token;
            })
            .then((token) => {
              console.log("获取token成功，执行队列");
              console.log(requests);
              requests.forEach((cb) => cb(token));
              requests = [];
            })
            .catch((res) => {
              console.error("getToken token error: ", res);
            });
        }
        return new Promise((resolve) => {
          requests.push((token) => {
            config.header = {
              Authorization: "Bearer " + token,
            };
            resolve(config);
          });
        });
      } else {
        config.header = {
          Authorization: "Bearer " + access_token,
        };
      }
    }
    return config;
  },
  (config) => {
    return Promise.reject(config);
  }
);

// 请求后
http.interceptors.response.use(
  (response) => {
    if (response.config.custom.loading) {
      loading--;
      if (loading == 0) {
        uni.hideLoading();
      }
    }
    var a = JSON.parse(JSON.stringify(response));
    var data = a.data;
    console.log(a.config.url + "返回", data);
    return response.data;
  },
  async (error) => {
    uni.hideLoading();
    loading = 0;
    const config = error.config;

    if (error.statusCode === 401) {
      if (!isRefreshing) {
        isRefreshing = true;

        try {
          // 获取新token
          const tokenRes = await getToken();
          const newToken = tokenRes.data;

          // 更新token
          http.setToken({
            token: newToken,
          });

          // 执行队列中的请求
          executeQueue(newToken);

          // 重试当前请求
          config.header = {
            Authorization: "Bearer " + newToken,
          };
          return http.request(config);
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);
          // 清空队列
          retryQueue.forEach((cb) => cb(null));
          retryQueue = [];
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        // 将请求加入队列
        return new Promise((resolve) => {
          retryQueue.push((token) => {
            if (token) {
              config.header = {
                Authorization: "Bearer " + token,
              };
              resolve(http.request(config));
            } else {
              resolve(Promise.reject(error));
            }
          });
        });
      }
    }
    return Promise.reject(error);
  }
);

export default http;
