<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="会议详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">申请信息</view>
        <view class="status status1" v-if="form.ApplyStatus == 0">{{ form.ApplyStatusName }}</view>
        <view class="status status3" v-else-if="form.ApplyStatus == 2">{{ form.ApplyStatusName }}</view>
        <view class="status status2" v-else>{{ form.ApplyStatusName }}</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">申请单号</view>
          <view class="flex-bd">{{ form.MeetingOrderNo }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">申请时间</view>
          <view class="flex-bd">{{ form.ApplyTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">申请人</view>
          <view class="flex-bd">{{ form.ApplyUserName }}</view>
        </view>
        <view class="form-item flex" @click="$c.callPhone(form.CellPhone)">
          <view class="label">联系电话</view>
          <view class="flex-bd">{{ form.CellPhone }}</view>
          <view class="flex-ft">
            <u-icon name="phone" :size="20" color="#999"></u-icon>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">会议名称</view>
          <view class="flex-bd">{{ form.MeetingName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">会议室</view>
          <view class="flex-bd">{{ form.ApplyRoomName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">开始时间</view>
          <view class="flex-bd">{{ form.ApplyFromTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">结束时间</view>
          <view class="flex-bd">{{ form.ApplyEndTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">会议主持</view>
          <view class="flex-bd">{{ form.MeetingHost }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">主席台名单</view>
          <view class="flex-bd">{{ form.RostrumList }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">参会人数</view>
          <view class="flex-bd">{{ form.PersonNumber }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">服务需求</view>
          <view class="flex-bd">{{ form.Demand }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.Remark && form.Remark != "null" ? form.Remark : "" }}</view>
        </view>
      </view>
    </view>

    <!-- 审核 -->
    <block v-if="form.ApplyStatus == 0 && isAdmin">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">审核</view>
        </view>
        <view class="form" style="margin: 0 40rpx">
          <u-form labelWidth="100">
            <u-form-item required label="审核结果" borderBottom>
              <u-radio-group v-model="checkForm.ApplyStatus">
                <u-radio :activeColor="$c.color()" :name="1" customStyle="margin-right: 20rpx">通过</u-radio>
                <u-radio :activeColor="$c.color()" :name="2" customStyle="margin-right: 20rpx">不通过</u-radio>
              </u-radio-group>
            </u-form-item>
            <block v-if="checkForm.ApplyStatus == 1">
              <u-form-item required label="会议室" borderBottom @click="roomShow = true">
                <u-input v-model="checkForm.RoomName" border="none" :disabled="true" disabledColor="#fff" placeholder="请输入会议室" />
                <u-icon name="arrow-right" slot="right"></u-icon>
              </u-form-item>
              <u-form-item required label="开始时间" borderBottom @click="showTimePicker('start')">
                <u-input v-model="checkForm.FromDateTime" border="none" :disabled="true" disabledColor="#fff" placeholder="请选择开始时间" />
                <u-icon name="arrow-right" slot="right"></u-icon>
              </u-form-item>
              <u-form-item required label="结束时间" borderBottom @click="showTimePicker('end')">
                <u-input v-model="checkForm.EndDateTime" border="none" :disabled="true" disabledColor="#fff" placeholder="请选择结束时间" />
                <u-icon name="arrow-right" slot="right"></u-icon>
              </u-form-item>
              <u-form-item required label="服务负责人" borderBottom @click="$c.selectPeople('meetingService', 1, form.ProjectCode, ['ServiceLeaderName', 'ServiceLeaderCode'])">
                <u-input v-model="checkForm.ServiceLeaderName" border="none" :disabled="true" disabledColor="#fff" placeholder="请选择服务负责人" />
                <u-icon name="arrow-right" slot="right"></u-icon>
              </u-form-item>
              <u-form-item required label="服务人员" borderBottom @click="$c.selectPeople('meetingService', 999, form.ProjectCode, ['ServiceUserNames', 'ServiceUserCodes'])">
                <u-input v-model="checkForm.ServiceUserNames" border="none" :disabled="true" disabledColor="#fff" placeholder="请选择服务人员" />
                <u-icon name="arrow-right" slot="right"></u-icon>
              </u-form-item>
            </block>
            <u-form-item label="备注">
              <u-textarea v-model="checkForm.CheckRemark" border="none" placeholder="请输入备注" />
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="pd30" v-if="form.ApplyStatus == 0 && isAdmin">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="check">审核</u-button>
      </view>
    </block>

    <!-- 审核信息 -->
    <block v-if="form.ApplyStatus > 0">
      <view class="form-pannel">
        <view class="pannel-title flex bor-b">
          <view class="title-content flex-bd">审核信息</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="form-item flex">
            <view class="label">审核时间</view>
            <view class="flex-bd">{{ form.CheckTimeCaption }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">审核人</view>
            <view class="flex-bd">{{ form.CheckUserName }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">审核结果</view>
            <view class="flex-bd">{{ form.ApplyStatus == 2 ? "不通过" : "通过" }}</view>
          </view>
          <block v-if="form.ApplyStatus != 2">
            <view class="form-item flex">
              <view class="label">会议室</view>
              <view class="flex-bd">{{ form.RoomName }}</view>
            </view>
            <view class="form-item flex">
              <view class="label">开始时间</view>
              <view class="flex-bd">{{ form.FromDateTimeCaption }}</view>
            </view>
            <view class="form-item flex">
              <view class="label">结束时间</view>
              <view class="flex-bd">{{ form.EndDateTimeCaption }}</view>
            </view>
            <view class="form-item flex">
              <view class="label">服务负责人</view>
              <view class="flex-bd">
                <view class="meetPeopleList">
                  <view class="li flex" v-for="item in form.MeetingUserList">
                    <view class="name">
                      {{ item.UserName }}
                    </view>
                    <view class="inline tag" v-if="item.IsLeader == 1">负责人</view>
                    <view class="status flex-bd">
                      {{ item.IsAccept == 1 ? "已接单" : "未接单" }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <view class="form-item flex">
            <view class="label">备注</view>
            <view class="flex-bd">{{ form.CheckRemark && form.CheckRemark != "null" ? form.CheckRemark : "" }}</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 会前检查提交表单 -->
    <block v-if="form.ApplyStatus > 2 && !form.InspectBeforeTimeCaption && isService">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">会前检查</view>
        </view>
        <view class="form" style="margin: 0 40rpx">
          <u-form labelWidth="80">
            <u-form-item required label="检查项" borderBottom>
              <view class="radio-list">
                <view class="radio-items" v-for="item in beforeCheckList">
                  <view class="title">{{ item.CheckContent }}</view>
                  <view class="radio">
                    <view class="radio-item" @click="updateSelected(item, true)">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.selected ? $c.color() : '#ccc'"></u-icon></view>
                      <view class="inline">是</view>
                    </view>
                    <view class="radio-item" @click="updateSelected(item, false)">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.selected ? '#ccc' : $c.color()"></u-icon></view>
                      <view class="inline">否</view>
                    </view>
                  </view>
                </view>
              </view>
            </u-form-item>
            <u-form-item required label="检查单" borderBottom>
              <my-upload v-model="fileList1" @change="beforeCheckUploadChange"></my-upload>
            </u-form-item>
            <u-form-item label="备注">
              <u-textarea v-model="beforeCheckForm.InspectBeforeRemark" border="none" placeholder="请输入备注" />
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="pd30">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="startMeetingCheck">提交</u-button>
      </view>
    </block>

    <!-- 会前检查结果 -->
    <block v-if="form.InspectBeforeTimeCaption">
      <view class="form-pannel">
        <view class="pannel-title flex bor-b">
          <view class="title-content flex-bd">会前检查</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="form-item flex">
            <view class="label">检查时间</view>
            <view class="flex-bd">{{ form.InspectBeforeTimeCaption }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">检查项</view>
            <view class="flex-bd">
              <view class="radio-list">
                <view class="radio-items" v-for="item in form.MeetingCheckList" v-if="item.CheckType == '会前'">
                  <view class="title">{{ item.Content }}</view>
                  <view class="radio">
                    <view class="radio-item">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.Result == 1 ? $c.color() : '#ccc'"></u-icon></view>
                      <view class="inline">是</view>
                    </view>
                    <view class="radio-item">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.Result == 0 ? $c.color() : '#ccc'"></u-icon></view>
                      <view class="inline">否</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="form-item flex">
            <view class="label">检查单</view>
            <view class="flex-bd">
              <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
            </view>
          </view>
          <view class="form-item flex">
            <view class="label">备注</view>
            <view class="flex-bd">{{ form.InspectBeforeRemark && form.InspectBeforeRemark != "null" ? form.InspectBeforeRemark : "" }}</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 会后检查提交表单，存在结束时间，但是没有会后检查时间 -->
    <block v-if="form.ApplyStatus > 2 && form.FactEndTimeCaption && !form.InspectAfterTimeCaption && isService">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">会后检查</view>
        </view>
        <view class="form" style="margin: 0 40rpx">
          <u-form labelWidth="80">
            <u-form-item required label="检查项" borderBottom>
              <view class="radio-list">
                <view class="radio-items" v-for="item in afterCheckList">
                  <view class="title">{{ item.CheckContent }}</view>
                  <view class="radio">
                    <view class="radio-item" @click="updateSelected(item, true)">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.selected ? $c.color() : '#ccc'"></u-icon></view>
                      <view class="inline">是</view>
                    </view>
                    <view class="radio-item" @click="updateSelected(item, false)">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.selected ? '#ccc' : $c.color()"></u-icon></view>
                      <view class="inline">否</view>
                    </view>
                  </view>
                </view>
              </view>
            </u-form-item>
            <u-form-item required label="检查单" borderBottom>
              <my-upload v-model="fileList2" @change="afterCheckUploadChange"></my-upload>
            </u-form-item>
            <u-form-item label="备注">
              <u-textarea v-model="afterCheckForm.InspectAfterRemark" border="none" placeholder="请输入备注" />
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="pd30">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="endMeetingCheck">提交</u-button>
      </view>
    </block>

    <!-- 会后检查结果 -->
    <block v-if="form.InspectAfterTimeCaption">
      <view class="form-pannel">
        <view class="pannel-title flex bor-b">
          <view class="title-content flex-bd">会后检查</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="form-item flex">
            <view class="label">检查时间</view>
            <view class="flex-bd">{{ form.InspectAfterTimeCaption }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">检查项</view>
            <view class="flex-bd">
              <view class="radio-list">
                <view class="radio-items" v-for="item in form.MeetingCheckList" v-if="item.CheckType == '会后'">
                  <view class="title">{{ item.Content }}</view>
                  <view class="radio">
                    <view class="radio-item">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.Result == 1 ? $c.color() : '#ccc'"></u-icon></view>
                      <view class="inline">是</view>
                    </view>
                    <view class="radio-item">
                      <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.Result == 0 ? $c.color() : '#ccc'"></u-icon></view>
                      <view class="inline">否</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="form-item flex">
            <view class="label">检查单</view>
            <view class="flex-bd">
              <u-upload name="1" multiple :maxCount="fileList2.length" :fileList="fileList2" :disabled="true" :deletable="false"></u-upload>
            </view>
          </view>
          <view class="form-item flex">
            <view class="label">备注</view>
            <view class="flex-bd">{{ form.InspectAfterRemark && form.InspectAfterRemark != "null" ? form.InspectAfterRemark : "" }}</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 回访 状态为已完成，ReturnUserName为空，且为负责人 -->
    <block v-if="form.ApplyStatus > 3 && !form.ReturnUserName && isAdmin">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">回访</view>
        </view>
        <view class="form" style="margin: 0 40rpx">
          <u-form labelWidth="80">
            <u-form-item required label="内容" borderBottom>
              <u-textarea v-model="returnvisitForm.EvaluateContent" border="none" placeholder="请输入内容" />
            </u-form-item>
            <u-form-item label="照片" v-if="fileList3.length > 0">
              <my-upload v-model="fileList3" @change="returnvisitUploadChange"></my-upload>
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="pd30">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="returnVisit">提交</u-button>
      </view>
    </block>

    <!-- 回访结果 -->
    <block v-if="form.ReturnEvaluateContent">
      <view class="form-pannel">
        <view class="pannel-title flex bor-b">
          <view class="title-content flex-bd">回访结果</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="form-item flex">
            <view class="label">回访人</view>
            <view class="flex-bd">{{ form.ReturnUserName }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">内容</view>
            <view class="flex-bd">{{ form.ReturnEvaluateContent && form.ReturnEvaluateContent != "null" ? form.ReturnEvaluateContent : "" }}</view>
          </view>
          <view class="form-item flex" v-if="fileList3.length > 0">
            <view class="label">照片</view>
            <view class="flex-bd">
              <u-upload name="1" multiple :maxCount="fileList3.length" :fileList="fileList3" :disabled="true" :deletable="false"></u-upload>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 评价信息 -->
    <view class="form-pannel" v-if="form.EvaluateTimeCaption">
      <view class="pannel-title bor-b">
        <view class="title-content">评价信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">评价星级</view>
          <view class="flex-bd">
            <u-rate :count="form.EvaluateLevel" v-model="form.EvaluateLevel"></u-rate>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">评价时间</view>
          <view class="flex-bd">{{ form.EvaluateTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">评价内容</view>
          <view class="flex-bd">{{ form.EvaluateContent && form.EvaluateContent != "null" ? form.EvaluateContent : "" }}</view>
        </view>
        <view class="form-item flex" v-if="fileList4.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList4.length" :fileList="fileList4" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>

    <!-- 开始会议,需要满足:有会前检查单，没有会后检查单，开始时间为空，而且是服务人员-->
    <view class="pd30" v-if="form.InspectBeforeTimeCaption && !form.InspectAfterTimeCaption && isService && !form.FactFromTimeCaption">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="start">开始会议</u-button>
    </view>

    <!-- 结束会议,需要满足:有开始时间，但是没有结束时间，而且是服务人员-->
    <view class="pd30" v-if="form.FactFromTimeCaption && !form.FactEndTimeCaption && isService">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="end">结束会议</u-button>
    </view>

    <!-- 催单 -->
    <view class="pd30" v-if="form.ApplyStatus == 1 && isAdmin">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="remind">催单</u-button>
    </view>

    <!-- 接单 -->
    <view class="pd30" v-if="form.ApplyStatus == 1 && canAccept">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="accept">接单</u-button>
    </view>

    <!-- 会议室选择器 -->
    <u-picker :show="roomShow" :columns="roomColumns" keyName="label" @cancel="roomShow = false" @confirm="roomConfirm"></u-picker>

    <!-- 时间选择器 -->
    <u-datetime-picker :show="timeShow" v-model="currentTime" @confirm="timeConfirm" @cancel="timeShow = false"></u-datetime-picker>

    <!-- 取消 -->
    <view class="pd30" v-if="form.ApplyStatus == 0 && UserCode == form.ApplyUserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="cancel">取消</u-button>
    </view>

    <!-- 评价 -->
    <view class="pd30" v-if="form.ApplyStatus == 4 && UserCode == form.ApplyUserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="$c.evaluate(Code, 'meeting')">评价</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserCode: uni.getStorageSync("UserCode"),
      UserInfo: uni.getStorageSync("UserInfo"),
      Code: "",
      ProjectCode: "",
      type: "",
      form: {},
      checkForm: {
        Code: "",
        ApplyStatus: 1,
        GlobalUserCode: uni.getStorageSync("UserCode"),
        CheckRemark: "",
        RoomCode: "",
        RoomName: "",
        FromDateTime: "",
        EndDateTime: "",
        ServiceLeaderCode: "",
        ServiceLeaderName: "",
        ServiceUserCodes: "",
        ServiceUserNames: "",
      },
      beforeCheckForm: {
        Code: "",
        GlobalUserCode: uni.getStorageSync("UserCode"),
        InspectBefore: "",
        InspectBeforeRemark: "",
        LatLon: "",
        CheckBeforeCodes: "",
      },
      afterCheckForm: {
        Code: "",
        GlobalUserCode: uni.getStorageSync("UserCode"),
        InspectAfter: "",
        InspectAfterRemark: "",
        LatLon: "",
        CheckAfterCodes: "",
      },
      returnvisitForm: {
        Code: "",
        GlobalUserCode: uni.getStorageSync("UserCode"),
        EvaluateContent: "",
        EvaluatePhotos: "",
      },
      roomShow: false,
      roomColumns: [],
      timeShow: false,
      currentTime: Number(new Date()),
      timeType: "",
      isAdmin: false, //是否会议服务负责人
      canAccept: false, //服务人员能否接单
      isService: false, //是否服务人员
      beforeCheckList: [], //会议检查单
      afterCheckList: [], //会议检查单
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
    };
  },
  onLoad(options) {
    this.ProjectCode = options.ProjectCode;
    this.Code = options.Code;
    this.type = options.type;
    this.checkForm.Code = this.Code;
    this.beforeCheckForm.Code = this.Code;
    this.afterCheckForm.Code = this.Code;
    this.returnvisitForm.Code = this.Code;
    this.isAdmin = this.UserInfo.CurRolesCaption.split(",").includes("会议服务负责人");
    this.getDetail();

    // 获取会议室列表
    this.$apis
      .getMeetingRoomList({
        ProjectCode: uni.getStorageSync("UserInfo").ProjectCodes,
      })
      .then((res) => {
        this.roomColumns = [
          res.data.map((item) => ({
            label: item.RoomName,
            value: item.Code,
          })),
        ];
      });

    //获取会前检查单
    this.$apis
      .getMeetingCheckList({
        ProjectCode: this.ProjectCode,
        CheckType: "会前",
      })
      .then((res) => {
        res.data.forEach((item) => {
          item.selected = true;
        });
        this.beforeCheckList = res.data;
      });

    //获取会后检查单
    this.$apis
      .getMeetingCheckList({
        ProjectCode: this.ProjectCode,
        CheckType: "会后",
      })
      .then((res) => {
        res.data.forEach((item) => {
          item.selected = true;
        });
        this.afterCheckList = res.data;
      });

    //获取经纬度
    uni.getLocation({
      type: "wgs84",
      success: (res) => {
        this.beforeCheckForm.LatLon = `${res.latitude},${res.longitude}`;
        this.afterCheckForm.LatLon = `${res.latitude},${res.longitude}`;
      },
    });
  },
  onShow() {
    uni.$on("selectPeople", (data) => {
      console.log(data);
      if (this.selectType === "leader") {
        this.checkForm[data.params[0]] = data.list[0].UserName;
        this.checkForm[data.params[1]] = data.list[0].Code;
      } else {
        this.checkForm[data.params[0]] = data.list.map((item) => item.UserName).join(",");
        this.checkForm[data.params[1]] = data.list.map((item) => item.Code).join(",");
      }
      uni.$off("selectPeople");
    });
    uni.$on("evaluateRefresh", (res) => {
      if (res) {
        this.getDetail();
      }
      uni.$off("evaluateRefresh");
    });
  },
  methods: {
    updateSelected(item, value) {
      this.$set(item, "selected", value);
    },
    beforeCheckUploadChange(data) {
      console.log(data);
      this.beforeCheckForm.InspectBefore = data.urls;
    },
    afterCheckUploadChange(data) {
      this.afterCheckForm.InspectAfter = data.urls;
    },
    returnvisitUploadChange(data) {
      this.returnvisitForm.EvaluatePhotos = data.urls;
    },
    getDetail() {
      this.$apis
        .getMeetingApplyDetail({
          Code: this.Code,
        })
        .then((res) => {
          this.form = res.data;
          this.checkForm.FromDateTime = this.form.ApplyFromTimeCaption;
          this.checkForm.EndDateTime = this.form.ApplyEndTimeCaption;
          this.checkForm.RoomCode = this.form.ApplyRoomCode;
          this.checkForm.RoomName = this.form.ApplyRoomName;
          for (let i in this.form.MeetingUserList) {
            if (this.form.MeetingUserList[i].ServiceUserCode == uni.getStorageSync("UserCode") && this.form.MeetingUserList[i].IsAccept == 0) {
              this.canAccept = true;
            }
            if (this.form.MeetingUserList[i].ServiceUserCode == uni.getStorageSync("UserCode")) {
              this.isService = true;
            }
          }
          if (this.form.InspectBefore) {
            this.fileList1 = this.form.InspectBefore.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          if (this.form.InspectAfter) {
            this.fileList2 = this.form.InspectAfter.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          if (this.form.ReturnEvaluatePhotos) {
            this.fileList3 = this.form.ReturnEvaluatePhotos.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          if (this.form.EvaluatePhotos) {
            this.fileList4 = this.form.EvaluatePhotos.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
        });
    },
    roomConfirm(e) {
      this.checkForm.RoomName = e.value[0].label;
      this.checkForm.RoomCode = e.value[0].value;
      this.roomShow = false;
    },
    showTimePicker(type) {
      this.timeType = type;
      this.timeShow = true;
    },
    timeConfirm(e) {
      const time = this.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:ss");
      if (this.timeType === "start") {
        this.checkForm.FromDateTime = time;
      } else {
        this.checkForm.EndDateTime = time;
      }
      this.timeShow = false;
    },
    selectServiceLeader() {
      this.selectType = "leader";
      uni.navigateTo({
        url: `../selectPeople/selectPeople?type=meetingService&num=1&projectCode=${this.form.ProjectCode}`,
      });
    },
    selectServiceUsers() {
      this.selectType = "users";
      uni.navigateTo({
        url: `../selectPeople/selectPeople?type=meetingService&num=999&projectCode=${this.form.ProjectCode}`,
      });
    },
    checkValidation() {
      if (this.checkForm.ApplyStatus == 1) {
        if (!this.checkForm.RoomName) {
          this.$u.toast("请选择会议室");
          return false;
        }
        if (!this.checkForm.FromDateTime) {
          this.$u.toast("请选择开始时间");
          return false;
        }
        if (!this.checkForm.EndDateTime) {
          this.$u.toast("请选择结束时间");
          return false;
        }
        if (!this.checkForm.ServiceLeaderCode) {
          this.$u.toast("请选择服务负责人");
          return false;
        }
        if (!this.checkForm.ServiceUserCodes) {
          this.$u.toast("请选择服务人员");
          return false;
        }
      }
      return true;
    },
    //回访
    returnVisit() {
      if (!this.returnvisitForm.EvaluateContent) {
        this.$u.toast("请输入内容");
        return;
      }
      this.$apis.returnvisitMeeting(this.returnvisitForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "提交成功" });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
        } else {
          this.$u.toast(res.msg);
        }
      });
    },
    //开始会议
    start() {
      uni.showModal({
        title: "提示",
        content: "确定开始会议吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .startMeeting({
                Code: this.Code,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "开始会议成功" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg || "开始会议失败");
                }
              });
          }
        },
      });
    },
    //结束会议
    end() {
      uni.showModal({
        title: "提示",
        content: "确定结束会议吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .endMeeting({
                Code: this.Code,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "结束会议成功" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg || "结束会议失败");
                }
              });
          }
        },
      });
    },
    //会前检查
    startMeetingCheck() {
      if (!this.beforeCheckForm.InspectBefore) {
        this.$u.toast("请上传检查单");
        return;
      }
      // 获取所有选中的检查项Code
      this.beforeCheckForm.CheckBeforeCodes = this.beforeCheckList
        .filter((item) => item.selected)
        .map((item) => item.Code)
        .join(",");
      this.$apis.startMeetingCheck(this.beforeCheckForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
        } else {
          this.$u.toast(res.msg || "提交失败");
        }
      });
    },
    //会后检查
    endMeetingCheck() {
      if (!this.afterCheckForm.InspectAfter) {
        this.$u.toast("请上传检查单");
        return;
      }
      // 获取所有选中的检查项Code
      this.afterCheckForm.CheckAfterCodes = this.afterCheckList
        .filter((item) => item.selected)
        .map((item) => item.Code)
        .join(",");
      this.$apis.endMeetingCheck(this.afterCheckForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "提交成功" });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
        } else {
          this.$u.toast(res.msg || "提交失败");
        }
      });
    },
    check() {
      if (!this.checkValidation()) return;

      this.$apis.checkMeetingApply(this.checkForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "审核成功", icon: "success", duration: 1500 });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
        } else {
          this.$u.toast(res.msg || "审核失败");
        }
      });
    },
    cancel() {
      uni.showModal({
        title: "提示",
        content: "确定取消吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .cancelMeetingApply({
                Code: this.Code,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "取消成功", icon: "success" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                  uni.$emit("meetRefresh", true);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
    remind() {
      this.$apis
        .remindMeetingApply({
          Code: this.Code,
        })
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "催单成功" });
          } else {
            this.$u.toast(res.msg);
          }
        });
    },
    accept() {
      uni.showModal({
        title: "提示",
        content: "确定接单吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .receiveMeetingApply({
                Code: this.Code,
                GlobalUserCode: uni.getStorageSync("UserCode"),
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "接单成功" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
.popup-box {
  position: relative;
  height: 900rpx;
}
.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  position: relative;
  font-size: 32rpx;
  border-bottom: 1px solid #eee;
}
.popup-header .close {
  position: absolute;
  right: 30rpx;
  font-size: 40rpx;
  color: #999;
}
.popup-body {
  height: calc(100% - 180rpx);
  padding: 20rpx 0;
}
.form-cells {
  background: #fff;
}
.form-cell {
  display: flex;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}
.form-cell .label {
  width: 200rpx;
  color: #666;
}
.form-cell .value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popup-footer {
  display: flex;
  height: 90rpx;
  border-top: 1px solid #eee;
}
.popup-footer > view {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
.popup-footer .btn-cancel {
  background: #f2f2f2;
  color: #666;
}
.popup-footer .btn-confirm {
  background: v-bind("$c.color()");
  color: #fff;
}
</style>
