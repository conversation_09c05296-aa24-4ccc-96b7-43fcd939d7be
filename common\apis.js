import http from "@/common/http/index.js";
var apis = {};
//用户
//获取验证码
apis.getSms = (form, config) => api("/PMSWebApi/Login/GetVerifyCode", "", form, config);
//登录

apis.login = (form, config) => api("/PMSWebApi/Login/Check", "", form, config);
//获取用户信息
apis.getUserInfo = (form, config) => api("/PMSWebApi/user/GetEntity", "", form, config);
//业主认证
apis.yzAuth = (form, config) => api("/PMSWebApi/Login/Execute", "yzcheck", form, config);
//员工认证
apis.ygAuth = (form, config) => api("/PMSWebApi/Login/Execute", "ygcheck", form, config);
//更新用户信息
apis.updateUserInfo = (form, config) => api("/PMSWebApi/user/Execute", "modify", form, config);
//更新手机号
apis.updatePhone = (form, config) => api("/PMSWebApi/user/Execute", "modifycellPhone", form, config);
//绑定openid
apis.bindOpenid = (form, config) => api("/PMSWebApi/Login/SetOpenId", "", form, config);
//获取openid
apis.getOpenid = (form, config) => api("/PMSWebApi/Login/GetOpenId", "", form, config);
//解绑openid
apis.unbindOpenid = (form, config) => api("/PMSWebApi/user/Execute", "cancelwxid", form, config);
//保存unionid
apis.getUnionid = (form, config) => api("/PMSWebApi/Login/GetUnionId", "", form, config);

//通知公告
//列表
apis.getNoticeList = (form, config) => api("/PMSWebApi/Notice/GetPageList", "", form, config);
//详情
apis.getNoticeDetail = (form, config) => api("/PMSWebApi/Notice/GetEntity", "", form, config);
//已读未读数量
apis.getNoticeReadCount = (form, config) => api("/PMSWebApi/Notice/GetAllByRead", "", form, config);
//设置已读
apis.setNoticeRead = (form, config) => api("/PMSWebApi/Notice/Execute", "isread", form, config);
//删除公告
apis.deleteNotice = (form, config) => api("/PMSWebApi/Notice/Execute", "delete", form, config);

//报修模块
//提交报修
apis.addRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "add", form, config);
//我的报修
apis.getMyRepairList = (form, config) => api("/PMSWebApi/Project/GetRepairPageListByProject", "", form, config);
//报修申请列表
apis.getRepairApplyList = (form, config) => api("/PMSWebApi/Repair/GetPageListByProject", "", form, config);
//报修详情
apis.getRepairDetail = (form, config) => api("/PMSWebApi/Repair/GetEntity", "", form, config);
//取消报修
apis.cancelRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "cancel", form, config);
//维修类型
apis.getRepairTypeList = (form, config) => api("/PMSWebApi/Repair/GetDataDicList", "RepairType", form, config);
//维修服务人员列表
apis.getRepairServiceList = (form, config) => api("/PMSWebApi/Repair/GetRepairUserByProject", "", form, config);
//维修派单
apis.dispatchRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "check", form, config);
//维修催单
apis.remindRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "reminder", form, config);
//确认接单
apis.confirmRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "receive", form, config);
//确认完成
apis.completeRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "complete", form, config);
//维修评价
apis.evaluateRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "evaluate", form, config);
//增加维修记录
apis.addRepairRecord = (form, config) => api("/PMSWebApi/Repair/Execute", "addhistory", form, config);
//获取记录列表
apis.getRepairRecordList = (form, config) => api("/PMSWebApi/Repair/GetRepairDealHistoryList", "", form, config);
//质量检测
apis.checkRepair = (form, config) => api("/PMSWebApi/Repair/Execute", "addcheck", form, config);

//项目模块
//获取项目列表
apis.getProjectList = (form, config) => api("/PMSWebApi/Project/GetAllProject", "", form, config);
//获取我的项目列表
apis.getMyProjectList = (form, config) => api("/PMSWebApi/Project/MyProJectList", "", form, config);
//获取项目详情
apis.getProjectInfo = (form, config) => api("/PMSWebApi/Project/GetEntity", "", form, config);
//项目菜单权限
apis.getProjectMenu = (form, config) => api("/PMSWebApi/Project/GetAllMenu", "", form, config);

//卤菜外卖模块
//申请列表
apis.getTakeoutApplyList = (form, config) => api("/PMSWebApi/TakeOut/GetPageListByProject", "", form, config);
//申请详情
apis.getTakeoutApplyDetail = (form, config) => api("/PMSWebApi/TakeOut/GetEntity", "", form, config);
//提交申请
apis.addTakeoutApply = (form, config) => api("/PMSWebApi/TakeOut/Execute", "add", form, config);
//修改
apis.updateTakeoutApply = (form, config) => api("/PMSWebApi/TakeOut/Execute", "modify", form, config);
//菜品列表
apis.getTakeoutDishList = (form, config) => api("/PMSWebApi/TakeOut/GetDishList", "", form, config);
//审核
apis.checkTakeoutApply = (form, config) => api("/PMSWebApi/TakeOut/Execute", "check", form, config);
//完成取餐
apis.confirmTakeout = (form, config) => api("/PMSWebApi/TakeOut/Execute", "complete", form, config);
//取消
apis.cancelTakeoutApply = (form, config) => api("/PMSWebApi/TakeOut/Execute", "cancel", form, config);
//评价
apis.evaluateTakeout = (form, config) => api("/PMSWebApi/TakeOut/Execute", "evaluate", form, config);

//餐食预留模块
//申请列表
apis.getReserveApplyList = (form, config) => api("/PMSWebApi/Reserve/GetPageListByProject", "", form, config);
//申请详情
apis.getReserveApplyDetail = (form, config) => api("/PMSWebApi/Reserve/GetEntity", "", form, config);
//提交申请
apis.addReserveApply = (form, config) => api("/PMSWebApi/Reserve/Execute", "add", form, config);
//修改
apis.updateReserveApply = (form, config) => api("/PMSWebApi/Reserve/Execute", "modify", form, config);
//审核
apis.checkReserveApply = (form, config) => api("/PMSWebApi/Reserve/Execute", "check", form, config);
//完成
apis.completeReserve = (form, config) => api("/PMSWebApi/Reserve/Execute", "complete", form, config);
//取消
apis.cancelReserveApply = (form, config) => api("/PMSWebApi/Reserve/Execute", "cancel", form, config);
//评价
apis.evaluateReserve = (form, config) => api("/PMSWebApi/Reserve/Execute", "evaluate", form, config);

//包厢预定模块
//申请列表
apis.getBoxApplyList = (form, config) => api("/PMSWebApi/Box/GetPageListByProject", "", form, config);
//申请详情
apis.getBoxApplyDetail = (form, config) => api("/PMSWebApi/Box/GetEntity", "", form, config);
//提交申请
apis.addBoxApply = (form, config) => api("/PMSWebApi/Box/Execute", "add", form, config);
//修改
apis.updateBoxApply = (form, config) => api("/PMSWebApi/Box/Execute", "modify", form, config);
//审核
apis.checkBoxApply = (form, config) => api("/PMSWebApi/Box/Execute", "check", form, config);
//完成
apis.completeBox = (form, config) => api("/PMSWebApi/Box/Execute", "complete", form, config);
//取消
apis.cancelBoxApply = (form, config) => api("/PMSWebApi/Box/Execute", "cancel", form, config);
//评价
apis.evaluateBox = (form, config) => api("/PMSWebApi/Box/Execute", "evaluate", form, config);

//会议预约模块
//申请列表
apis.getMeetingApplyList = (form, config) => api("/PMSWebApi/Meeting/GetPageListByProject", "", form, config);
//申请详情
apis.getMeetingApplyDetail = (form, config) => api("/PMSWebApi/Meeting/GetEntity", "", form, config);
//提交申请
apis.addMeetingApply = (form, config) => api("/PMSWebApi/Meeting/Execute", "add", form, config);
//会议室列表
apis.getMeetingRoomList = (form, config) => api("/PMSWebApi/Meeting/GetRoomList", "", form, config);
//会议服务列表
apis.getMeetingServiceList = (form, config) => api("/PMSWebApi/Meeting/GetDataDicList", "", form, config);
//会议服务人员列表
apis.getMeetingServiceUserList = (form, config) => api("/PMSWebApi/Meeting/GetMeetingUserByProject", "", form, config);
//修改
apis.updateMeetingApply = (form, config) => api("/PMSWebApi/Meeting/Execute", "modify", form, config);
//取消
apis.cancelMeetingApply = (form, config) => api("/PMSWebApi/Meeting/Execute", "cancel", form, config);
//审核
apis.checkMeetingApply = (form, config) => api("/PMSWebApi/Meeting/Execute", "check", form, config);
//催单
apis.remindMeetingApply = (form, config) => api("/PMSWebApi/Meeting/Execute", "reminder", form, config);
//确认接单
apis.receiveMeetingApply = (form, config) => api("/PMSWebApi/Meeting/Execute", "receive", form, config);
//会议检查要求
apis.getMeetingCheckList = (form, config) => api("/PMSWebApi/Meeting/GetMeetingCheckList", "", form, config);
//会前检查
apis.startMeetingCheck = (form, config) => api("/PMSWebApi/Meeting/Execute", "startcheck", form, config);
//会后检查
apis.endMeetingCheck = (form, config) => api("/PMSWebApi/Meeting/Execute", "endcheck", form, config);
//开始会议
apis.startMeeting = (form, config) => api("/PMSWebApi/Meeting/Execute", "start", form, config);
//结束会议
apis.endMeeting = (form, config) => api("/PMSWebApi/Meeting/Execute", "end", form, config);
//会议回访
apis.returnvisitMeeting = (form, config) => api("/PMSWebApi/Meeting/Execute", "returnvisit", form, config);
//评价
apis.evaluateMeeting = (form, config) => api("/PMSWebApi/Meeting/Execute", "evaluate", form, config);

//能耗模块
//能耗列表
apis.getEnergyList = (form, config) => api("/PMSWebApi/Meter/GetPageList", "", form, config);
//能耗详情
apis.getEnergyDetail = (form, config) => api("/PMSWebApi/Meter/GetEntity", "", form, config);
//能耗记录增加
apis.addEnergyRecord = (form, config) => api("/PMSWebApi/Meter/Execute", "add", form, config);
//能耗记录列表
apis.getEnergyRecordList = (form, config) => api("/PMSWebApi/Meter/GetPageListByReading", "", form, config);
//能耗记录详情
apis.getEnergyRecordDetail = (form, config) => api("/PMSWebApi/Meter/GetEntityByReading", "", form, config);

//巡检
//巡检列表
apis.getInspectList = (form, config) => api("/PMSWebApi/inspection/GetEquipmentPageList", "", form, config);
//巡检详情
apis.getInspectDetail = (form, config) => api("/PMSWebApi/inspection/GetEquipmentBizQueryEntity", "", form, config);
//巡检记录增加
apis.addInspectRecord = (form, config) => api("/PMSWebApi/inspection/Execute", "add", form, config);
//巡检记录列表
apis.getInspectRecordList = (form, config) => api("/PMSWebApi/inspection/GetPageList", "", form, config);
//巡检记录详情
apis.getInspectRecordDetail = (form, config) => api("/PMSWebApi/inspection/GetInspectionEntity", "", form, config);
//分类信息
apis.getInspectCategoryList = (form, config) => api("/PMSWebApi/inspection/GetEquipmentCateBizQuery", "", form, config);
//位置信息
apis.getInspectLocationList = (form, config) => api("/PMSWebApi/inspection/GetEquipmentLocation", "", form, config);
//巡检标准列表
apis.getInspectStandardList = (form, config) => api("/PMSWebApi/inspection/GetEquipmentRemarkEntity", "", form, config);
//巡检数量统计
apis.getInspectCount = (form, config) => api("/PMSWebApi/inspection/GetCountYcAndCq", "", form, config);
//巡检人员列表
apis.getInspectUserList = (form, config) => api("/PMSWebApi/inspection/GetUserByProjectAndModule", "", form, config);

//资产模块
//资产列表
apis.getAssetList = (form, config) => api("/PMSWebApi/Asset/GetPageList", "", form, config);
//资产详情
apis.getAssetDetail = (form, config) => api("/PMSWebApi/Asset/GetEntity", "", form, config);
//增加资产
apis.addAsset = (form, config) => api("/PMSWebApi/Asset/Execute", "add", form, config);
//修改资产
apis.updateAsset = (form, config) => api("/PMSWebApi/Asset/Execute", "modify", form, config);

//日报模块
//日报列表
apis.getDailyList = (form, config) => api("/PMSWebApi/daily/GetDailyPageList", "", form, config);
//日报详情
apis.getDailyDetail = (form, config) => api("/PMSWebApi/daily/GetDailyBizQueryEntity", "", form, config);
//增加日报
apis.addDaily = (form, config) => api("/PMSWebApi/daily/Execute", "add", form, config);

//设备巡检数量统计模块名称(设备巡检，安防巡逻，保洁监管，安全检查，质量检查，网格巡检)
apis.getInspectCount = (form, config) => api("/PMSWebApi/Project/CountOverInspection", "", form, config);
//能耗管理数量
apis.getEnergyCount = (form, config) => api("/PMSWebApi/Project/CountOverMeterReading", "", form, config);
//资产管理数量
apis.getAssetCount = (form, config) => api("/PMSWebApi/Project/CountAsset", "", form, config);
//卤菜外卖数量
apis.getTakeoutCount = (form, config) => api("/PMSWebApi/Project/CountTakeOutApply", "", form, config);
//餐食预留数量
apis.getReserveCount = (form, config) => api("/PMSWebApi/Project/CountReserveApply", "", form, config);
//包厢预定数量
apis.getBoxCount = (form, config) => api("/PMSWebApi/Project/CountBoxApply", "", form, config);
//会务预约数量
apis.getMeetingCount = (form, config) => api("/PMSWebApi/Project/CountMeeting", "", form, config);
//工作日报数量
apis.getDailyCount = (form, config) => api("/PMSWebApi/Project/CountDaily", "", form, config);
//维修工单数量
apis.getRepairCount = (form, config) => api("/PMSWebApi/Project/CountRepair", "", form, config);

//我的预约
apis.getMyOrderList = (form, config) => api("/PMSWebApi/Project/GetYyPageListByProject", "", form, config);

//考勤管理
//列表
apis.getAttendanceList = (form, config) => api("/PMSWebApi/Attendance/GetPageList", "", form, config);
//详情
apis.getAttendanceDetail = (form, config) => api("/PMSWebApi/Attendance/GetEntity", "", form, config);
//增加
apis.addAttendance = (form, config) => api("/PMSWebApi/Attendance/Execute", "add", form, config);
//修改
apis.updateAttendance = (form, config) => api("/PMSWebApi/Attendance/Execute", "modify", form, config);
//删除
apis.delAttendance = (form, config) => api("/PMSWebApi/Attendance/Execute", "delete", form, config);

//缴费通知
//列表
apis.getPaymentNotice = (form, config) => api("/PMSWebApi/DeliveryNotice/GetPageList", "", form, config);
//详情
apis.getPaymentNoticeDetail = (form, config) => api("/PMSWebApi/DeliveryNotice/GetEntity", "", form, config);

//通讯录
apis.getContactsList = (form, config) => api("/PMSWebApi/Contact/UserList", "", form, config);
function api(api, docmd, form, config) {
  return new Promise((resolve, reject) => {
    http
      .post(
        api,
        {
          DoCmd: docmd,
          ...form,
        },
        { custom: { ...config } }
      )
      .then((res) => {
        resolve(res);
      })
      .catch((res) => {
        reject(res);
      });
  });
}

export default apis;
