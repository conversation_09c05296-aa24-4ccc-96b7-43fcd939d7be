<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="餐食预留详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">申请信息</view>
        <view class="status status1" v-if="form.ApplyStatus == 0">{{ form.ApplyStatusCaption }}</view>
        <view class="status status3" v-else-if="form.ApplyStatus == 2">{{ form.ApplyStatusCaption }}</view>
        <view class="status status2" v-else>{{ form.ApplyStatusCaption }}</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">申请单号</view>
          <view class="flex-bd">{{ form.ReserveOrderNo }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">申请人</view>
          <view class="flex-bd">{{ form.ApplyUserName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">申请时间</view>
          <view class="flex-bd">{{ form.ApplyTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">用餐时间</view>
          <view class="flex-bd">{{ form.ReserveTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">用餐人数</view>
          <view class="flex-bd">{{ form.Number }}</view>
        </view>
        <view class="form-item flex" @click="$c.callPhone(form.CellPhone)">
          <view class="label">联系电话</view>
          <view class="flex-bd">{{ form.CellPhone }}</view>
          <view class="flex-ft">
            <u-icon name="phone" :size="20" color="#999"></u-icon>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.Remark }}</view>
        </view>
      </view>
    </view>

    <!-- 管理员审核按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 0 && isAdmin">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="showCheckPopup = true">审核</u-button>
    </view>

    <!-- 管理员确认用餐按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 1 && isAdmin">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="confirm">确认用餐</u-button>
    </view>

    <!-- 用户取消预约按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 0 && UserCode == form.ApplyUserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="cancel">取消预约</u-button>
    </view>

    <!-- 用户评价按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 3 && UserCode == form.ApplyUserCode && !form.EvaluateTimeCaption">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="$c.evaluate(Code, 'reserve')">评价</u-button>
    </view>

    <!-- 评价信息 -->
    <view class="form-pannel" v-if="form.EvaluateTimeCaption">
      <view class="pannel-title bor-b">
        <view class="title-content">评价信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">评价星级</view>
          <view class="flex-bd">
            <u-rate :count="form.EvaluateLevel" v-model="form.EvaluateLevel"></u-rate>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">评价时间</view>
          <view class="flex-bd">{{ form.EvaluateTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">评价内容</view>
          <view class="flex-bd">{{ form.EvaluateContent }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>

    <u-popup :show="showCheckPopup" mode="bottom" :closeable="true" @close="showCheckPopup = false">
      <view class="popup-content">
        <view class="form-pannel">
          <view class="pannel-title bor-b">
            <view class="title-content">审核</view>
          </view>
          <view class="form" style="padding: 10rpx 0">
            <u-form labelWidth="80">
              <u-form-item required label="审核结果" borderBottom>
                <u-radio-group v-model="checkForm.ApplyStatus">
                  <u-radio :activeColor="$c.color()" :name="1" customStyle="margin-right: 20rpx">通过</u-radio>
                  <u-radio :activeColor="$c.color()" :name="2" customStyle="margin-right: 20rpx">不通过</u-radio>
                </u-radio-group>
              </u-form-item>
              <u-form-item label="备注" borderBottom>
                <u-textarea v-model="checkForm.CheckRemark" border="none" placeholder="请输入备注" />
              </u-form-item>
            </u-form>
          </view>
        </view>
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="check">确认</u-button>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      fileList1: [],
      showCheckPopup: false,
      checkForm: {
        Code: "",
        ApplyStatus: "", //1通过，2不通过
        GlobalUserCode: uni.getStorageSync("UserCode"),
        CheckRemark: "",
      },
      UserCode: uni.getStorageSync("UserCode"),
      UserInfo: uni.getStorageSync("UserInfo"),
      Code: "",
      form: {},
      isAdmin: false,
    };
  },
  onShow() {
    uni.$on("evaluateRefresh", (res) => {
      if (res) {
        this.getDetail();
      }
      uni.$off("evaluateRefresh");
    });
  },
  onLoad(options) {
    this.Code = options.Code;
    this.checkForm.Code = this.Code;
    this.isAdmin = this.UserInfo.CurRolesCaption.split(",").includes("餐食预留服务人员");
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.$apis
        .getReserveApplyDetail({
          Code: this.Code,
        })
        .then((res) => {
          this.form = res.data;
          this.fileList1 = res.data.EvaluatePhotos.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        });
    },
    check() {
      if (!this.checkForm.ApplyStatus) {
        this.$u.toast("请选择审核结果");
        return;
      }
      this.showCheckPopup = false;
      uni.showModal({
        title: "提示",
        content: "确定提交吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis.checkReserveApply(this.checkForm).then((res) => {
              if (res.code == 100) {
                uni.showToast({ mask: true, title: "审核成功", icon: "success" });
                setTimeout(() => {
                  this.getDetail();
                }, 1500);
              } else {
                this.$u.toast(res.msg);
              }
            });
          }
        },
      });
    },
    confirm() {
      uni.showModal({
        title: "提示",
        content: "确定用餐吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .completeReserve({
                Code: this.Code,
                GlobalUserCode: this.UserCode,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "确认成功", icon: "success" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
    cancel() {
      uni.showModal({
        title: "提示",
        content: "确定取消预约吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .cancelReserveApply({
                Code: this.Code,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "取消成功", icon: "success" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
