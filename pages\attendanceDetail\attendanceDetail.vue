<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">考勤信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">应到人数</view>
          <view class="flex-bd">{{ form.ReachedNum }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">实到人数</view>
          <view class="flex-bd">{{ form.PresentNum }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.Remark }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>
    <view class="fixbtn">
      <view class="btns flex">
        <view class="btn bor" @click="del">删除</view>
        <view class="btn" @click="$c.naviTo('../attendanceAdd/attendanceAdd?Code=' + form.Code)">修改</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      fileList1: [],
      Code: "",
    };
  },
  onLoad(e) {
    this.Code = e.Code;
    this.getDetail();
  },
  onShow() {
    uni.$on("refreshAttendanceDetail", () => {
      this.getDetail();
    });
  },
  onUnload() {
    uni.$off("refreshAttendanceDetail");
  },
  methods: {
    getDetail() {
      this.$apis.getAttendanceDetail({ Code: this.Code }).then((res) => {
        this.form = res.data;
        this.fileList1 = res.data.Smallimage.split(",").map((item) => {
          return {
            url: this.$http.config.staticURL + item,
          };
        });
      });
    },
    del() {
      uni.showModal({
        title: "提示",
        content: "确定删除吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis.delAttendance({ Code: this.Code }).then((res) => {
              if (res.code == 103) {
                uni.showToast({ mask: true, title: "删除成功", icon: "success" });
                uni.$emit("refreshAttendanceList");
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              } else {
                this.$u.toast(res.msg);
              }
            });
          }
        },
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
