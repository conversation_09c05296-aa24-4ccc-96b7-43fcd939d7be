<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="选择人员" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="people-list">
      <view class="li flex bor-b" @click="select(item)" v-for="(item, index) in list">
        <view class="flex-hd">
          <u-icon name="checkmark-circle-fill" :color="item.selected ? $c.color() : '#ddd'" size="18"></u-icon>
        </view>
        <view class="image">
          <image :src="$c.getFullImage(item.HeadImg)" v-if="item.HeadImg" mode="aspectFill"></image>
          <image src="../../static/images/user-avatar1.png" v-else mode="aspectFill"></image>
        </view>
        <view class="flex-bd">
          <view class="top flex">
            <view class="name flex-bd">
              <view>{{ item.UserName }}</view>
              <view style="font-size: 24rpx; color: #999; margin-top: 5rpx">{{ item.CurRolesCaption }}</view>
            </view>
            <view class="tag">{{ item.JobNumber ? item.JobNumber : "" }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30" style="margin-top: 40rpx" v-if="num > 1">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">确认</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      params: [],
      type: "",
      num: 1,
    };
  },
  onLoad(options) {
    this.type = options.type;
    this.num = options.num;
    this.params = options.params;
    if (this.type == "repairService") {
      this.$apis
        .getRepairServiceList({
          ProjectCode: options.projectCode,
        })
        .then((res) => {
          res.data.forEach((item) => (item.selected = false));
          this.list = res.data;
        });
    }
    if (this.type == "meetingService") {
      this.$apis
        .getMeetingServiceUserList({
          ProjectCode: options.projectCode,
        })
        .then((res) => {
          res.data.forEach((item) => (item.selected = false));
          this.list = res.data;
        });
    }
    if (this.type == "inspectUser") {
      this.$apis
        .getInspectUserList({
          ProjectCode: options.projectCode,
          ModuleName: options.moduleName, //设备巡检，安防巡逻，保洁监管，安全检查，质量检查，网格巡检
          UserName: "",
        })
        .then((res) => {
          res.data.forEach((item) => (item.selected = false));
          this.list = res.data;
        });
    }
  },
  methods: {
    select(item) {
      if (this.num == 1) {
        uni.$emit("selectPeople", {
          list: [item],
          params: this.params.split(","),
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 100);
      }
      item.selected = !item.selected;
    },
    submit() {
      let selectedList = this.list.filter((item) => item.selected);
      if (selectedList.length == 0) {
        this.$c.toast("请选择人员");
        return;
      }
      uni.$emit("selectPeople", {
        list: selectedList,
        params: this.params.split(","),
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 100);
    },
  },
};
</script>

<style></style>
