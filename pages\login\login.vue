<template>
  <view class="login">
    <view class="login-title">欢迎登录</view>
    <view class="login-subtitle">物业管理系统</view>
    <view class="login-type">
      <u-tabs :list="typeList" @click="typeClick" :scrollable="false" :lineColor="$c.color()" activeStyle="font-size: 32rpx;font-weight: bold;"></u-tabs>
    </view>
    <view class="login-form">
      <view class="login-item">
        <u-input v-model="CellPhone" customStyle="height:60rpx" border="none" type="number" placeholder="请输入手机号"></u-input>
      </view>
      <view class="login-item flex">
        <view class="flex-bd">
          <u-input v-model="VerifyCode" customStyle="height:60rpx" border="none" type="number" placeholder="请输入验证码"></u-input>
        </view>
        <view class="btn" @click="getVerifyCode">{{ tips }}</view>
      </view>
      <view class="login-btn">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="login">登录</u-button>
      </view>
    </view>
    <u-code ref="uCode" @change="codeChange" seconds="60" @start="disabled1 = true" @end="disabled1 = false"></u-code>
  </view>
</template>

<script>
export default {
  data() {
    return {
      CellPhone: "",
      VerifyCode: "",
      UserType: 1,
      typeList: [
        {
          name: "业主",
          value: "1",
        },
        {
          name: "员工",
          value: "2",
        },
      ],
      tips: "",
      disabled1: false,
    };
  },
  onLoad() {
    if (uni.getStorageSync("UserCode")) {
      if (uni.getStorageSync("UserInfo").UserType == 1) {
        uni.reLaunch({
          url: "../yzHome/yzHome",
        });
      } else {
        uni.reLaunch({
          url: "../index/index",
        });
      }
    }
  },
  methods: {
    typeClick(e) {
      this.UserType = e.value;
    },
    codeChange(text) {
      this.tips = text;
    },
    getVerifyCode() {
      if (!this.CellPhone) {
        return this.$u.toast("请输入手机号");
      }
      if (!this.$u.test.mobile(this.CellPhone)) {
        return this.$u.toast("请输入正确的手机号");
      }
      if (this.$refs.uCode.canGetCode) {
        // 模拟向后端请求验证码
        uni.showLoading({
          title: "正在获取验证码",
        });
        this.$apis
          .getSms(
            {
              CellPhone: this.CellPhone,
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            uni.hideLoading();
            // 这里此提示会被this.start()方法中的提示覆盖
            this.$u.toast(res.msg);
            // 通知验证码组件内部开始倒计时
            this.$refs.uCode.start();
          });
      } else {
        this.$u.toast("倒计时结束后再发送");
      }
    },
    login() {
      if (!this.formValidation()) return;

      this.$apis
        .login({
          CellPhone: this.CellPhone.trim(),
          VerifyCode: this.VerifyCode.trim(),
          UserType: this.UserType,
        })
        .then((res) => {
          if (res.code == 100) {
            // 保存登录信息
            uni.setStorageSync("UserCode", res.data.UserInfo.Code);
            var UserInfo = res.data.UserInfo;
            var ProjectCodes = UserInfo.ProjectCodes;
            var ProjectCodesCaption = UserInfo.ProjectCodesCaption;
            if (res.data.UserInfo.UserType == 1) {
              uni.setStorageSync("ProjectCodes", ProjectCodes);
              uni.setStorageSync("ProjectCodesCaption", ProjectCodesCaption);
              UserInfo.ProjectCodes = ProjectCodes ? ProjectCodes.split(",")[0] : "";
              UserInfo.ProjectCodesCaption = ProjectCodesCaption ? ProjectCodesCaption.split(",")[0] : "";
            }
            console.log(UserInfo);
            uni.setStorageSync("UserInfo", UserInfo);
            uni.setStorageSync("UserType", this.UserType);

            uni.showToast({ mask: true, title: "登录成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              if (res.data.UserInfo.UserType == 1) {
                uni.reLaunch({
                  url: "/pages/yzHome/yzHome",
                });
              } else {
                uni.reLaunch({
                  url: "/pages/index/index",
                });
              }
            }, 1500);
          } else {
            this.$u.toast(res.msg || "登录失败");
            this.VerifyCode = ""; // 清空验证码
          }
        })
        .catch((err) => {
          this.$u.toast("登录失败，请重试");
          console.error(err);
          this.VerifyCode = ""; // 清空验证码
        });
    },
    formValidation() {
      if (!this.$u.test.mobile(this.CellPhone)) {
        this.$u.toast("请输入正确的手机号");
        return false;
      }

      if (!this.VerifyCode) {
        this.$u.toast("请输入验证码");
        return false;
      }
      return true;
    },
  },
};
</script>

<style></style>
