<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="通知详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="notice-detail">
      <view class="title">{{ detail.Title }}</view>
      <view class="info">
        <view class="time">
          <view class="inline" style="margin-right: 10rpx">
            <u-icon name="clock" color="#999" size="12"></u-icon>
          </view>
          发布时间：{{ detail.CreateDateTimeCaption }}
        </view>
      </view>
      <view class="content">
        <u-parse :content="detail.NoticeContent"></u-parse>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      Code: "",
      detail: {},
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.getDetail();
    //设置已读
    // this.$apis.setNoticeRead({
    //   Code: this.Code,
    //   UserCode: uni.getStorageSync("UserCode"),
    // });
  },
  methods: {
    // 获取通知详情
    getDetail() {
      this.$apis
        .getPaymentNoticeDetail({
          Code: this.Code,
          UserCode: uni.getStorageSync("UserCode"),
        })
        .then((res) => {
          if (res.data) {
            this.detail = res.data;
          }
        });
    },
  },
};
</script>

<style>
page {
  background: #fff;
}
.notice-detail {
  padding: 30rpx;
}
.notice-detail .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}
.notice-detail .info {
  display: flex;
  align-items: center;
  margin: 20rpx 0 30rpx;
  font-size: 24rpx;
  color: #999;
}
.notice-detail .info .time {
  margin-right: 30rpx;
}
.notice-detail .content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
</style>
