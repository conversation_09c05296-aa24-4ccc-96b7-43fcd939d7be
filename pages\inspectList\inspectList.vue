<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" :title="ModuleName" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <view class="search-box" @click="searchShow = true">
        <u-search disabled placeholder="搜索" :showAction="false" shape="square"></u-search>
      </view>
    </view>
    <view class="scroll" style="padding-top: 54px">
      <view class="repaire-list">
        <view class="li" @click="$c.naviTo('../inspectDetail/inspectDetail?Code=' + item.Code + '&moduleName=' + ModuleName)" v-for="(item, index) in list">
          <view class="li-top flex">
            <view class="left flex-bd">{{ item.EquipmentName }}</view>
          </view>
          <view class="li-bottom">
            <view class="flex">
              <view class="flex-hd">{{ label.EquipmentNumber }}</view>
              <view class="flex-bd">{{ item.EquipmentNumber }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">{{ label.InspectionTime }}</view>
              <view class="flex-bd">{{ item.CycleType == "按周期" ? item.InspectionTime + item.InspectionUnit : item.InspectionWeek + item.TimeRanges }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">{{ label.LastInspectionTime }}</view>
              <view class="flex-bd line-1">{{ $c.formatTime(item.LastInspectionTime) }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <u-popup :show="searchShow" mode="top" bgColor="transparent" overlayOpacity="0.1" zIndex="10006" @close="searchShow = false">
      <view class="pop-select" :style="'padding-top: ' + statusBarHeight + 'px;'">
        <view class="title flex">
          <view class="flex-bd">筛选</view>
          <view class="flex-hd" @click="searchShow = false">
            <u-icon name="close" :size="20"></u-icon>
          </view>
        </view>
        <scroll-view class="pd030" :scroll-y="true" style="box-sizing: border-box">
          <view class="form">
            <u-form labelWidth="80">
              <!-- 状态 -->
              <u-form-item required label="状态" borderBottom>
                <view class="status-group">
                  <view v-for="(item, index) in statusList" :key="index" class="status-item" :class="{ active: searchData.OverdueCaption === item.value }" @click="searchData.OverdueCaption = item.value">
                    {{ item.name }}
                  </view>
                </view>
              </u-form-item>
              <!-- 名称 -->
              <u-form-item :label="label.EquipmentName" borderBottom>
                <u-input v-model="searchData.EquipmentName" border="none" placeholder="请输入" />
              </u-form-item>
              <!-- 编号 -->
              <u-form-item :label="label.EquipmentNumber" borderBottom>
                <u-input v-model="searchData.EquipmentNumber" border="none" placeholder="请输入" />
              </u-form-item>
              <!-- 检查人 -->
              <!-- <u-form-item :label="label.InspectorUserName" borderBottom>
                <u-input v-model="searchData.InspectorUserName" border="none" placeholder="请输入" />
              </u-form-item> -->
              <!-- 分类 -->
              <u-form-item :label="label.CateName" borderBottom @click="$c.naviTo('../selectCate/selectCate?type=category&projectCode=' + ProjectCode + '&moduleName=' + ModuleName)">
                <u-input v-model="searchData.CateName" disabled disabledColor="#fff" border="none" placeholder="请选择" />
                <u-icon name="arrow-right" :size="20" slot="right"></u-icon>
              </u-form-item>
              <!-- 地址 -->
              <u-form-item :label="label.LocationName" borderBottom @click="$c.naviTo('../selectCate/selectCate?type=location&projectCode=' + ProjectCode + '&moduleName=' + ModuleName)">
                <u-input v-model="searchData.LocationName" disabled disabledColor="#fff" border="none" placeholder="请选择" />
                <u-icon name="arrow-right" :size="20" slot="right"></u-icon>
              </u-form-item>
            </u-form>
          </view>
          <view style="padding: 50rpx 0">
            <view class="flex" style="gap: 30rpx">
              <u-button :ripple="true" :hairline="false" type="info" @click="searchShow = false">取消</u-button>
              <u-button :ripple="true" :hairline="false" :customStyle="'background: ' + $c.color() + ';color:#fff;border:0'" type="primary" @click="(popShow = false), search()">搜索</u-button>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchShow: false,
      statusBarHeight: 0,
      divHei: 0,
      scrollHei: 0,
      statusList: [
        { name: "全部", value: "" },
        { name: "已超期", value: "超期" },
        { name: "未超期", value: "正常" },
      ],
      searchData: {
        EquipmentName: "", //设备名称
        EquipmentCode: "", //设备编号
        InspectorUserName: "", //巡检人
        CateCode: "", //设备分类（精确）
        CateName: "",
        LocationCode: "", //设备地址（精确）
        LocationName: "",
        OverdueCaption: "", //状态
      },
      list: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      ModuleName: "",
      ProjectCode: "",
      label: {},
    };
  },
  onShow() {
    uni.$on("selectCate", (item) => {
      console.log(item);
      this.searchData.CateName = item.CateName;
      this.searchData.CateCode = item.Code;
      uni.$off("selectCate");
    });
    uni.$on("selectLocation", (item) => {
      this.searchData.LocationName = item.LocationName;
      this.searchData.LocationCode = item.Code;
      uni.$off("selectLocation");
    });

    uni.$on("refreshInspectList", (res) => {
      console.log(res);
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
      uni.$off("refreshInspectList");
    });
  },
  onLoad(options) {
    this.ModuleName = options.ModuleName;
    this.ProjectCode = options.ProjectCode;
    var sys = uni.getSystemInfoSync();
    this.statusBarHeight = sys.statusBarHeight;
    this.divHei = sys.windowHeight - sys.statusBarHeight;
    this.scrollHei = sys.windowHeight - sys.statusBarHeight - 44;

    this.label = this.$c.switchInspectionLabel(this.ModuleName);

    // 初始加载数据
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    search() {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
        this.searchShow = false;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getInspectList(
            {
              ...Object.fromEntries(Object.entries(this.searchData).filter(([_, value]) => value !== null && value !== undefined && value !== "")),
              ProjectCode: this.ProjectCode, //项目编号
              ProjectName: "", //项目名称
              CurUserCode: uni.getStorageSync("UserInfo").Code || "", //当前登入人
              ModuleName: this.ModuleName,
              PageIndex: page,
              PageSize: 10,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style lang="scss">
page {
  background: #f6f6f6;
}
.repaire-list .li-bottom .flex-hd {
  width: 180rpx;
}
</style>
