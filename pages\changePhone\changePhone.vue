<template>
  <view class="userInfo">
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="更换手机号" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="u-info">
      <view class="form">
        <view class="item bor-b flex">
          <view class="flex-hd">新手机号</view>
          <view class="flex-bd">
            <u-input border="none" placeholder="请输入" v-model="form.CellPhone"></u-input>
          </view>
        </view>
        <view class="item bor-b flex">
          <view class="flex-hd">验证码</view>
          <view class="flex-bd">
            <u-input border="none" placeholder="请输入" v-model="form.Verification"></u-input>
          </view>
          <view class="btn" @click="getVerifyCode">{{ tips }}</view>
        </view>
      </view>
    </view>
    <view class="mr30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
    <u-code ref="uCode" @change="codeChange" seconds="60" @start="disabled1 = true" @end="disabled1 = false"></u-code>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tips: "",
      disabled1: false,
      form: {
        Code: uni.getStorageSync("UserInfo").Code, //用户编号
        CellPhone: "", //真实姓名
        Verification: "",
      },
    };
  },
  methods: {
    codeChange(text) {
      this.tips = text;
    },
    getVerifyCode() {
      if (!this.form.CellPhone) {
        return this.$u.toast("请输入手机号");
      }
      if (!this.$u.test.mobile(this.form.CellPhone)) {
        return this.$u.toast("请输入正确的手机号");
      }
      if (this.$refs.uCode.canGetCode) {
        // 模拟向后端请求验证码
        uni.showLoading({
          title: "正在获取验证码",
        });
        this.$apis
          .getSms(
            {
              CellPhone: this.form.CellPhone,
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            uni.hideLoading();
            // 这里此提示会被this.start()方法中的提示覆盖
            this.$u.toast(res.msg);
            // 通知验证码组件内部开始倒计时
            this.$refs.uCode.start();
          });
      } else {
        this.$u.toast("倒计时结束后再发送");
      }
    },
    submit() {
      if (!this.form.CellPhone) {
        return this.$u.toast("请输入手机号");
      }
      if (!this.$u.test.mobile(this.form.CellPhone)) {
        return this.$u.toast("请输入正确的手机号");
      }
      if (!this.form.Verification) {
        return this.$u.toast("请输入验证码");
      }
      this.$apis.updatePhone(this.form).then((res) => {
        if (res.code === 100) {
          uni.showToast({mask:true,
            title: "修改成功",
            icon: "success",
          });
          // 更新缓存手机号
          const userInfo = uni.getStorageSync("UserInfo");
          userInfo.CellPhone = this.form.CellPhone;
          uni.setStorageSync("UserInfo", userInfo);
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          this.$u.toast(res.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss"></style>
