<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="选择服务" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="service-list">
      <view class="li flex bor-b" @click="select(item)" v-for="(item, index) in list" :key="index">
        <view class="flex-hd">
          <u-icon name="checkmark-circle-fill" :color="item.selected ? $c.color() : '#ddd'" size="18"></u-icon>
        </view>
        <view class="flex-bd">{{ item.DataDicName }}</view>
      </view>
    </view>
    <view class="pd30" style="margin-top: 40rpx">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">确认</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
    };
  },
  onLoad() {
    this.$apis
      .getMeetingServiceList({
        Code: "ServiceDemand",
      })
      .then((res) => {
        this.list = res.data.map((item) => ({
          ...item,
          selected: false,
        }));
      });
  },
  methods: {
    select(item) {
      item.selected = !item.selected;
    },
    submit() {
      const selectedServices = this.list.filter((item) => item.selected).map((item) => item.DataDicName);

      if (selectedServices.length === 0) {
        this.$u.toast("请选择服务");
        return;
      }

      uni.$emit("selectService", selectedServices);
      setTimeout(() => {
        uni.navigateBack();
      }, 100);
    },
  },
};
</script>

<style></style>
