import http from "../common/http/index.js";
var common = {};

common.naviTo = function (e) {
  if (e) {
    uni.navigateTo({
      url: e,
    });
  }
};
common.naviToC = function (e) {
  if (e) {
    if (!uni.getStorageSync("UserCode")) {
      uni.showToast({
        icon: "error",
        title: "请登录",
      });
      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/shop/login",
        });
      }, 1500);
    } else {
      uni.navigateTo({
        url: e,
      });
    }
  }
};
common.tabberSwitch = function (e) {
  if (e) {
    uni.switchTab({
      url: e,
    });
  }
};
common.login = function (e) {
  uni.navigateTo({
    url: "/pages/shop/login",
  });
};

common.price = function (e) {
  if (e == 0) {
    return ["0", "00"];
  }
  if (e) {
    e = e.toString();
    if (e.indexOf(".") > -1) {
      var p = Number(e).toFixed(2);
      var ps = p.toString().split(".");
      return [ps[0], ps[1]];
    } else {
      return [Number(e), "00"];
    }
  }
};
common.priceStyle = function (e) {
  e = e.toString();
  if (e.indexOf(".") > -1) {
    return Number(e).toFixed(2);
  } else {
    return Number(e);
  }
};
common.copy = function (e) {
  uni.setClipboardData({
    data: e,
    success: function () {
      uni.showToast({
        icon: "none",
        title: "复制成功",
      });
    },
  });
};

common.color = function () {
  return "#08cc91";
};

common.btnStyle = function () {
  return "background: linear-gradient(135deg, #08cc91,#19c2d1);border:0px;height:90rpx;border-radius:8px;";
};
common.btnStylePlain = function () {
  return "border:1px solid #08cc91;height:90rpx;border-radius:8px;color:#08cc91;";
};

common.btnBg = function () {
  return "background: linear-gradient(135deg, #08cc91,#19c2d1);";
};

common.getFullImage = function (e) {
  if (e) {
    return http.config.staticURL + e;
  }
  return "";
};

common.viewImage = function (e, index) {
  console.log(e, index);
  uni.previewImage({
    urls: e,
    current: index,
  });
};

common.split = function (e) {
  if (e) {
    var a = "";
    var arr = e.split(",");
    for (let i in arr) {
      a = a + arr[i] + " ";
    }
    return a;
  } else {
    return;
  }
};
common.selectPeople = function (e, num, projectCode, params, args) {
  var text = "";
  if (args) {
    for (let i in args) {
      text = text + "&" + i + "=" + args[i];
    }
  }
  uni.navigateTo({
    url: "/pages/selectPeople/selectPeople?type=" + e + "&num=" + num + "&projectCode=" + projectCode + "&params=" + params.join(",") + text,
  });
};

common.evaluate = function (e, type) {
  uni.navigateTo({
    url: "/pages/evaluate/evaluate?Code=" + e + "&Type=" + type,
  });
};

common.callPhone = function (e) {
  uni.makePhoneCall({
    phoneNumber: e,
  });
};

common.formatDate = function (e) {
  if (!e) return "";
  return e.split("T")[0]; // Returns YYYY-MM-DD format
};
common.formatTime = function (e) {
  if (!e) return "";
  let dateTime = e.split("T");
  if (dateTime.length !== 2) return e;

  let time = dateTime[1].split(".")[0]; // Remove milliseconds if present
  return dateTime[0] + " " + time; // Returns YYYY-MM-DD HH:mm:ss format
};
common.getText = function (e) {
  if (!e) return "";
  return e.replace(/<[^>]+>/g, "");
};

common.switchInspectionLabel = function (e) {
  if (e == "专项检查") {
    return {
      Type: "检查",
      EquipmentName: "检查名称",
      EquipmentNumber: "检查编号",
      InspectionTime: "检查周期",
      LastInspectionTime: "最后一次检查",
      EquipmentCode: "检查编号",
      InspectorUserName: "检查人",
      CateName: "检查分类",
      LocationName: "检查地址",
      InspectionContent: "检查内容",
      InspectionTime: "检查时间",
      Images: "检查照片"
    };
  }
  if (e == "设备巡检") {
    return {
      Type: "巡检",
      EquipmentName: "巡检名称",
      EquipmentNumber: "巡检编号",
      InspectionTime: "巡检周期",
      LastInspectionTime: "最后一次巡检",
      EquipmentCode: "巡检编号",
      InspectorUserName: "巡检人",
      CateName: "巡检分类",
      LocationName: "巡检地址",
      InspectionContent: "巡检内容",
      InspectionTime: "巡检时间",
      Images: "巡检照片"
    };
  }
  if (e == "安防巡逻") {
    return {
      Type: "巡逻",
      EquipmentName: "巡逻名称",
      EquipmentNumber: "巡逻编号",
      InspectionTime: "巡逻周期",
      LastInspectionTime: "最后一次巡逻",
      EquipmentCode: "巡逻编号",
      InspectorUserName: "巡逻人",
      CateName: "巡逻分类",
      LocationName: "巡逻地址",
      InspectionContent: "巡逻内容",
      InspectionTime: "巡逻时间",
      Images: "巡逻照片"
    };
  }
  if (e == "保洁监管") {
    return {
      Type: "监管",
      EquipmentName: "监管名称",
      EquipmentNumber: "监管编号",
      InspectionTime: "监管周期",
      LastInspectionTime: "最后一次监管",
      EquipmentCode: "监管编号",
      InspectorUserName: "监管人",
      CateName: "监管分类",
      LocationName: "监管地址",
      InspectionContent: "监管内容",
      InspectionTime: "监管时间",
      Images: "监管照片"
    }
  }
  if (e == "维保管理") {
    return {
      Type: "检查",
      EquipmentName: "检查名称",
      EquipmentNumber: "检查编号",
      InspectionTime: "检查周期",
      LastInspectionTime: "最后一次检查",
      EquipmentCode: "检查编号",
      InspectorUserName: "检查人",
      CateName: "检查分类",
      LocationName: "检查地址",
      InspectionContent: "检查内容",
      InspectionTime: "检查时间",
      Images: "检查照片"
    };
  }
  if (e == "食材验收") {
    return {
      Type: "验收",
      EquipmentName: "验收名称",
      EquipmentNumber: "验收编号",
      InspectionTime: "验收周期",
      LastInspectionTime: "最后一次验收",
      EquipmentCode: "验收编号",
      InspectorUserName: "验收人",
      CateName: "验收分类",
      LocationName: "验收地址",
      InspectionContent: "验收内容",
      InspectionTime: "验收时间",
      Images: "验收照片"
    };
  }
  if (e == "绿化巡逻") {
    return {
      Type: "巡检",
      EquipmentName: "巡逻名称",
      EquipmentNumber: "巡逻编号",
      InspectionTime: "巡逻周期",
      LastInspectionTime: "最后一次巡逻",
      EquipmentCode: "巡逻编号",
      InspectorUserName: "巡逻人",
      CateName: "巡逻分类",
      LocationName: "巡逻地址",
      InspectionContent: "巡逻内容",
      InspectionTime: "巡逻时间",
      Images: "巡逻照片"
    };
  }
};

/**
 * 上传单个文件
 * @param {Object} file - 文件对象
 * @returns {Promise} 上传结果
 */
common.uploadFilePromise = function (file) {
  return new Promise((resolve, reject) => {
    http
      .upload(
        "/PMSWebApi/Config/Upload",
        {
          filePath: file.url || file,
          name: "file",
        },
        { custom: { loading: false } }
      )
      .then((res) => {
        if (res.code === 100) {
          resolve(res.data);
        } else {
          reject(res);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

/**
 * 处理u-upload组件的afterRead事件
 * @param {Object} event - 上传事件对象
 * @param {Object} fileList - 文件列表引用
 * @returns {Promise} 处理结果
 */
common.handleUploadAfterRead = async function (event, fileList) {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  const lists = [].concat(event.file);

  // 先添加文件到列表，状态为上传中
  const startIndex = fileList.length;
  lists.forEach(item => {
    const newFile = {
      ...item,
      status: "uploading",
      message: "上传中"
    };
    fileList.push(newFile);
  });

  // 逐个上传文件
  for (let i = 0; i < lists.length; i++) {
    try {
      const result = await this.uploadFilePromise(lists[i]);
      console.log(result);
      if (result && result.ServerFileName) {
        // 使用索引直接访问文件
        const currentIndex = startIndex + i;
        if (fileList[currentIndex]) {
          const updatedFile = {
            ...fileList[currentIndex],
            status: "success",
            message: "",
            url: http.config.staticURL + result.ServerFileName,
            pic: result.ServerFileName,
            Code: result.Code,
          };

          // 使用splice方法确保Vue响应式更新
          fileList.splice(currentIndex, 1, updatedFile);
        }
      } else {
        throw new Error("上传返回数据异常");
      }
    } catch (err) {
      console.error("Upload failed:", err);
      // 使用索引直接访问文件
      const currentIndex = startIndex + i;
      if (fileList[currentIndex]) {
        const errorFile = {
          ...fileList[currentIndex],
          status: "error",
          message: "上传失败"
        };
        fileList.splice(currentIndex, 1, errorFile);
      }
      uni.showToast({
        title: "上传失败",
        icon: "none",
      });
    }
  }
};

/**
 * 处理u-upload组件的delete事件
 * @param {Object} event - 删除事件对象
 * @param {Object} fileList - 文件列表引用
 */
common.handleUploadDelete = function (event, fileList) {
  fileList.splice(event.index, 1);
};

export default common;
