<template>
  <view>
    <u-navbar :auto-back="true" :placeholder="true" leftIconColor="#fff" title="资产管理" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <view class="search-box">
        <u-search v-model="AssetNameorNum" placeholder="请输入关键词" clearable @clear="search" @search="search" @custom="search" />
      </view>
    </view>
    <view class="scroll" style="padding-top: 54px">
      <view class="asset-list">
        <view class="li flex" @click="$c.naviTo('../assetDetail/assetDetail?Code=' + item.Code)" v-for="(item, index) in list">
          <view class="image">
            <image v-if="item.AssetPic" :src="item.AssetPic" mode="aspectFill" />
            <image v-else src="@/static/images/sbnopic.png" mode="aspectFill" />
          </view>
          <view class="flex-bd">
            <view class="name">资产名称: {{ item.AssetName }}</view>
            <view class="infos">资产编号：{{ item.AssetNumber }}</view>
          </view>
          <view class="flex-ft">
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <view class="float-btn" @click="$c.naviTo('../assetAdd/assetAdd?ProjectCode=' + ProjectCode)">
      <u-icon name="plus" color="#fff" size="24"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ProjectCode: "",
      AssetNameorNum: "",
      list: [],
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
    };
  },
  onLoad(options) {
    this.ProjectCode = options.ProjectCode;
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  onShow() {
    uni.$on("refreshAssetList", () => {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
      uni.$off("refreshAssetList");
    });
  },
  methods: {
    getList(page) {
      return new Promise((resolve, reject) => {
        this.$apis
          .getAssetList(
            {
              PageIndex: page,
              PageSize: 10,
              AssetNameorNum: this.AssetNameorNum,
              ProjectCode: this.ProjectCode,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            if (res.data.length > 0) {
              for (let index in res.data) {
                if (this.$c.getFullImage(res.data[index].AssetPic)) {
                  res.data[index].AssetPic = this.$http.config.staticURL + res.data[index].AssetPic.split(",")[0];
                } else {
                  res.data[index].AssetPic = "";
                }
              }
            }
            resolve(res.data);
          });
      });
    },
    search() {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
