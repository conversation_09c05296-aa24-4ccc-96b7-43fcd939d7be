<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="选择项目" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="project-list">
      <view class="project-item flex" v-for="(item, index) in list" @click="selectProject(index)">
        <view class="flex-hd">
          <image :src="$c.getFullImage(item.MainImage)" mode="aspectFill"></image>
        </view>
        <view class="flex-bd">
          <view class="project-name">
            <view class="name">{{ item.ProjectName }}</view>
            <view class="des">
              <view>地址：{{ item.Address }}</view>
              <view>项目负责人：{{ item.UserName }}</view>
            </view>
            <!-- <view class="status flex">
              <view class="status-item flex">
                <view>待处理工单</view>
                <view class="num">2</view>
              </view>
              <view class="status-item flex">
                <view>待处理工单</view>
                <view class="num">2</view>
              </view>
            </view> -->
          </view>
        </view>
        <view class="flex-ft">
          <u-icon name="arrow-right"></u-icon>
        </view>
        <view class="selected-icon" v-if="item.selected">
          <u-icon name="checkbox-mark" color="#fff" size="18"></u-icon>
        </view>
      </view>
    </view>
    <view class="pd30" style="margin-top: 40rpx" v-if="UserInfo.UserType == 2">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">确认</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      UserInfo: uni.getStorageSync("UserInfo"),
    };
  },
  onLoad(options) {
    this.$apis
      .getMyProjectList({
        UserCode: uni.getStorageSync("UserCode"),
      })
      .then((res) => {
        res.data.forEach((item) => (item.selected = false));
        this.list = res.data;
      });
  },
  methods: {
    selectProject(index) {
      if (this.UserInfo.UserType == 2) {
        this.list[index].selected = !this.list[index].selected;
      } else {
        uni.$emit("selectProject", this.list[index]);
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 100);
      }
    },
    submit() {
      let selectedList = this.list.filter((item) => item.selected);
      if (selectedList.length == 0) {
        this.$c.toast("请选择项目");
        return;
      }
      uni.$emit("selectProject", {
        Code: selectedList.map((item) => item.Code).join(","),
        ProjectName: selectedList.map((item) => item.ProjectName).join(","),
      });
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
        });
      }, 100);
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
