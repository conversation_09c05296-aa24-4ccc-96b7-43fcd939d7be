<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="预约列表" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <u-tabs :scrollable="false" @click="tabClick" :list="tabs" :lineColor="$c.color()" :activeStyle="'color:' + $c.color()"></u-tabs>
    </view>
    <view class="scroll" style="padding-top: 44px">
      <view class="repaire-list">
        <view class="li flex" @click="detail(item)" v-for="(item, index) in list">
          <view class="icon icon1" v-if="item.ModuleName == '包厢预定'">
            <i class="iconfont icon-baoxiang"></i>
          </view>
          <view class="icon icon2" v-if="item.ModuleName == '会议预约'">
            <i class="iconfont icon-huiyishi"></i>
          </view>
          <view class="icon icon3" v-if="item.ModuleName == '卤菜外卖'">
            <i class="iconfont icon-waimai"></i>
          </view>
          <view class="icon icon4" v-if="item.ModuleName == '餐食预留'">
            <i class="iconfont icon-canshi"></i>
          </view>
          <view class="flex-bd">
            <view class="li-top flex">
              <view class="left flex-bd">{{ item.ModuleName }}:{{ item.MeetingOrderNo }}</view>
            </view>
            <view class="li-bottom">
              <view class="flex">
                <view class="flex-hd">预约时间</view>
                <view class="flex-bd">{{ $c.formatDate(item.ApplyFromTime) }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      type: "",
      tabs: [
        { name: "待审核", value: "待审核" },
        { name: "预约成功", value: "预约成功" },
        { name: "预约失败", value: "预约失败" },
        { name: "已完成", value: "已完成" },
      ],
      list: [],
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
      MeetingStatusSearch: "待审核",
      ProjectCode: "",
    };
  },
  onLoad(options) {
    this.ProjectCode = options.ProjectCode;
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    tabClick(e) {
      if (this.MeetingStatusSearch != e.name) {
        this.MeetingStatusSearch = e.name;
        this.page = 1;
        this.list = [];
        this.getList(1).then((res) => {
          this.list = res;
        });
      }
    },
    detail(item) {
      if (item.ModuleName == "会议预约") {
        uni.navigateTo({ url: "../meetDetail/meetDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
      } else if (item.ModuleName == "卤菜外卖") {
        uni.navigateTo({ url: "../takeoutDetail/takeoutDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
      } else if (item.ModuleName == "餐食预留") {
        uni.navigateTo({ url: "../reserveDetail/reserveDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
      } else if (item.ModuleName == "包厢预定") {
        uni.navigateTo({ url: "../boxDetail/boxDetail?Code=" + item.Code + "&ProjectCode=" + item.ProjectCode });
      }
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getMyOrderList(
            {
              ProjectCode: this.ProjectCode ? this.ProjectCode : uni.getStorageSync("UserInfo").ProjectCodes,
              RepairStatusSearch: this.MeetingStatusSearch, //预约成功、预约失败、待审核、已完成
              ApplyUserCode: uni.getStorageSync("UserCode"),
              RepairOrderNo: "",
              PageIndex: page,
              PageSize: 10,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
