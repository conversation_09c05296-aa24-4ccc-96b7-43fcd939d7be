{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/login/login",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/yzHome/yzHome",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/repaireAdd/repaireAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/user/user",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/auth/auth",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/selectProject/selectProject",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/userInfo/userInfo",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/changePhone/changePhone",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/bindOpenid/bindOpenid",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/noticeList/noticeList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/noticeDetail/noticeDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/repaireDetail/repaireDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/contacts/contacts",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/projectIndex/projectIndex",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/repaireList/repaireList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/selectPeople/selectPeople",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/meetAdd/meetAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/takeoutAdd/takeoutAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/reserveAdd/reserveAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/evaluate/evaluate",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/dishList/dishList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/takeoutDetail/takeoutDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/takeoutList/takeoutList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/reserveDetail/reserveDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/reserveList/reserveList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/boxAdd/boxAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/boxDetail/boxDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/boxList/boxList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/meetDetail/meetDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/meetList/meetList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/selectMeetingService/selectMeetingService",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/inspectList/inspectList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/orderList/orderList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/energyList/energyList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/energyDetail/energyDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/energyCheckAdd/energyCheckAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/energyCheckDetail/energyCheckDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/selectCate/selectCate",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/inspectDetail/inspectDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/inspectCheckAdd/inspectCheckAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/inspectCheckDetail/inspectCheckDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/assetList/assetList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/assetDetail/assetDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/assetAdd/assetAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/dailyReportList/dailyReportList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/dailyReportAdd/dailyReportAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/dailyReportDetail/dailyReportDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/test/test",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/yzSelectProject/yzSelectProject",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/attendanceList/attendanceList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/attendanceDetail/attendanceDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/attendanceAdd/attendanceAdd",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/payNoticeList/payNoticeList",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/payNoticeDetail/payNoticeDetail",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "物业管理系统",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#000",
    "borderStyle": "black",
    "backgroundColor": "#fff",
    "selectedColor": "#000",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页"
      },
      {
        "pagePath": "pages/yzHome/yzHome",
        "text": "首页"
      },
      {
        "pagePath": "pages/contacts/contacts",
        "text": "通讯录"
      },
      {
        "pagePath": "pages/user/user",
        "text": "我的"
      }
    ]
  },
  "uniIdRouter": {}
}
