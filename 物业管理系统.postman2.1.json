{"info": {"name": "物业管理系统", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [], "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "env": [], "item": [{"name": "包厢预定", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "评价", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"evaluate\",\r\n    \"Code\":\"aa828ae5-b9e5-4b6b-8b4e-1baecbc18419\",\r\n    \"EvaluateLevel\":\"5\",\r\n    \"EvaluateContent\":\"nice\",\r\n    \"EvaluatePhotos\":\"/upload/...\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"check\",\r\n    \"Code\":\"aa828ae5-b9e5-4b6b-8b4e-1baecbc18419\",\r\n    \"ApplyStatus\":\"1\",//1通过，2不通过\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CheckRemark\":\"-\",\r\n    \"BoxName\":\"1号包厢\",\r\n    \"BoxFromTime\":\"2025-02-10 18:00:00\",\r\n    \"BoxContacts\":\"张三\",\r\n    \"BoxContactsPhone\":\"13912243351\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"add\",\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"FromTime\":\"2025-02-10 18:00:00\",\r\n    \"Number\":\"2\",\r\n    \"AvgMoney\":\"20\",\r\n    \"TotalMoney\":\"40\",\r\n    \"Remark\":\"-\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核历史", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/api/ReserveBoxMngApi/GetDetail?Docmd=history", "host": ["api"], "query": [{"key": "Docmd", "value": "history"}], "variable": [], "path": ["ReserveBoxMngApi", "GetDetail"]}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"1\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/GetPageListByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "GetPageListByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"b577c74b-1973-4a58-ab61-c43d0b9f7a0e\",\r\n    \"ApplyStatusSearch\":\"已完成\",//待处理，待用餐，已完成\r\n    \"BoxOrderNo\":\"\",\r\n    \"PageIndex\":1,\r\n    \"PageSize\":10\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "确认取餐", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"complete\",\r\n    \"Code\":\"aa828ae5-b9e5-4b6b-8b4e-1baecbc18419\",\r\n    \"GlobalUserCode\":\"15996697224\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "取消", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"cancel\",\r\n    \"Code\":\"aa828ae5-b9e5-4b6b-8b4e-1baecbc18419\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "修改", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Box/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Box", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"modify\",\r\n    \"Code\":\"aa828ae5-b9e5-4b6b-8b4e-1baecbc18419\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"FromTime\":\"2025-02-10 18:00:00\",\r\n    \"Number\":\"3\",\r\n    \"AvgMoney\":\"15\",\r\n    \"TotalMoney\":\"45\",\r\n    \"Remark\":\"-\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTYzNjc2LCJleHAiOjE3MzkxNzA4NzYsImlhdCI6MTczOTE2MzY3Nn0._4grt-UjyaUYhhwRSJSke8uM2vhs6VMrKRdyCti6__g", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "卤菜外卖", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "审核历史", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/api/VegetablesTakeoutMngApi/GetDetail?Docmd=history", "host": ["api"], "query": [{"key": "Docmd", "value": "history"}], "variable": [], "path": ["VegetablesTakeoutMngApi", "GetDetail"]}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "修改", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"modify\",\r\n    \"Code\":\"ed309b64-3210-4746-b9c3-0f18a6179655\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"OutTime\":\"2025-02-10 18:00:00\",\r\n    \"Remark\":\"-\",\r\n    \"TakeOutFoodList\": [\r\n        {\r\n            \"DishCode\":\"dd53c594-aaa4-4b77-80d1-59c89e1f0737\",\r\n            \"Number\":\"2\"\r\n        },\r\n    ]\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "确认取餐", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"complete\",\r\n    \"Code\":\"ed309b64-3210-4746-b9c3-0f18a6179655\",\r\n    \"GlobalUserCode\":\"15996697224\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "取消", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"cancel\",\r\n    \"Code\":\"ed309b64-3210-4746-b9c3-0f18a6179655\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"check\",\r\n    \"Code\":\"ed309b64-3210-4746-b9c3-0f18a6179655\",\r\n    \"ApplyStatus\":\"1\",//1通过，2不通过\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CheckRemark\":\"-\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "评价", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"evaluate\",\r\n    \"Code\":\"ed309b64-3210-4746-b9c3-0f18a6179655\",\r\n    \"EvaluateLevel\":\"5\",\r\n    \"EvaluateContent\":\"nice\",\r\n    \"EvaluatePhotos\":\"/upload/...\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "菜品列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/GetDishList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "GetDishList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"ed309b64-3210-4746-b9c3-0f18a6179655\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/GetPageListByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "GetPageListByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"ApplyStatusSearch\":\"已完成\",//待处理，待用餐，已完成\r\n    \"TakeOutOrderNo\":\"\",\r\n    \"PageIndex\":1,\r\n    \"PageSize\":10\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/TakeOut/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "TakeOut", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"add\",\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"OutTime\":\"2025-02-10 18:00:00\",\r\n    \"Remark\":\"-\",\r\n    \"TakeOutFoodList\": [\r\n        {\r\n            \"DishCode\":\"dd53c594-aaa4-4b77-80d1-59c89e1f0737\",\r\n            \"Number\":\"1\"\r\n        },\r\n    ]\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjQzLCJleHAiOjE3MzkxODU0NDMsImlhdCI6MTczOTE3ODI0M30.IYxpzBc9w25Zg_g-gO7CUPGFF2UiYgs3D8UWxZ4BPac", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "日报", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/daily/GetDailyBizQueryEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "daily", "GetDailyBizQueryEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"1\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjcxNTkwLCJleHAiOjE3MzkyNzg3OTAsImlhdCI6MTczOTI3MTU5MH0.Gwahz3wyB1W8PpphD8uuSUPpU8qKnUrom3cesQFzfR4", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/daily/GetDailyPageList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "daily", "GetDailyPageList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"\", //项目编号\r\n    \"CurUserCode\": \"11\", //当前登入人\r\n    \"DailyTime\": \"\", //填报时间\r\n    \"DailyUserName\": \"\",  //填报人\r\n    \"DailyUserCode\":\"\" //查询使用的人员 编号\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjcxNTkwLCJleHAiOjE3MzkyNzg3OTAsImlhdCI6MTczOTI3MTU5MH0.Gwahz3wyB1W8PpphD8uuSUPpU8qKnUrom3cesQFzfR4", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/daily/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "daily", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\": \"add\",\r\n    \"DailyUserCode\": \"123\",\r\n    \"DailyContent\": \"123\",\r\n    \"Images\": \"123\",\r\n    \"ProjectCode\": \"123\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjcxNTkwLCJleHAiOjE3MzkyNzg3OTAsImlhdCI6MTczOTI3MTU5MH0.Gwahz3wyB1W8PpphD8uuSUPpU8qKnUrom3cesQFzfR4", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "配置信息", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "获取数据字典信息", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Config/GetDicAll", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Config", "GetDicAll"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\": \"RoomType\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc0MTM2LCJleHAiOjE3MzkxODEzMzYsImlhdCI6MTczOTE3NDEzNn0.Dg9tdp6piX8oZmnYO40AG2ZsjxuQDG1co4dVXZicYIM", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "文件上传", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Config/Upload", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Config", "Upload"]}, "header": [], "body": {"mode": "formdata", "raw": "{}", "formdata": [{"key": "file", "value": ["C:\\Users\\<USER>\\Desktop\\微信图片_20250205163155.jpg"], "type": "text"}]}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc0MTM2LCJleHAiOjE3MzkxODEzMzYsImlhdCI6MTczOTE3NDEzNn0.Dg9tdp6piX8oZmnYO40AG2ZsjxuQDG1co4dVXZicYIM", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "微信消息发送", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Config/SendMessage", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Config", "SendMessage"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"MsgTitle\": \"ce\", //标题\r\n    \"MsgContent\": \"ce\", //  消息内容\r\n    \"MsgUserCode\": \"ce\", //发送用户\r\n    \"MsgUrl\": \"ce\", //跳转接口\r\n    \"CreateUserCode\": \"ce\", //创建人\r\n    \"SendType\": \"1\", //消息类型\r\n    \"WxTemplateCode\": \"ce\", //模板消息\r\n    \"WxMsgContent\":\"ce\" //模板消息内容\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjYwNDcxLCJleHAiOjE3MzkyNjc2NzEsImlhdCI6MTczOTI2MDQ3MX0.5OHo4qJHeNukwOFdyKpGiFbud1amm397SRVjecMSVrs", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "会议预约", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "服务需求列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会后检查", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "催单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "服务人员列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "结束会议", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会议反馈", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "开始会议", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会议室列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会议回访", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会前检查", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "接单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "取消", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "评价", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核历史", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会议检查要求", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "修改", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "能耗管理", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "抄表记录详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Meter/GetEntityByReading", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON>er", "GetEntityByReading"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\": \"ce9ff3cd-3c65-454a-b4ce-ec5eb8d37b16\" //主键编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjQzNzM5LCJleHAiOjE3MzkyNTA5MzksImlhdCI6MTczOTI0MzczOX0.CwDFUTLNDvl12ZG2Scd7vfiy5jh_qfBl04dR4GvSaQc", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "抄表记录新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Meter/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON>er", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n   \"DoCmd\": \"add\",\r\n   \"MeterCode\": \"b0333bc1-44cb-426a-9882-7af9797c94e6\",//水电表编码\r\n   \"MeterNumber\": \"000001\",//表号\r\n   \"MeterType\": \"水\",//水电表类型\r\n   \"MeterValue\": \"000000\",//表数值\r\n   \"ReadingTime\": \"2025-06-09\",//用电日期\r\n   \"Remark\": \"测试备注\",//备注\r\n   \"Images\": \"/upload/20250209011943.png\",//图片\r\n   \"ReadingUserCode\": \"18361769116\",//抄表人编号\r\n   \"SignImage\": \"\",//签名图片\r\n   \"IsOver\": 0,//是否超期抄表\r\n   \"LatLon\": \"\",//经纬度信息\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjU5NDczLCJleHAiOjE3MzkyNjY2NzMsImlhdCI6MTczOTI1OTQ3M30.ZfpFtTvybaphhZWNDhGliAvhx77AqGnJaRGQ6qWdu7I", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "水电表列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Meter/GetPageList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON>er", "GetPageList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10,\r\n    \"MeterNumber\": \"\",//表号\r\n    \"Address\": \"\",//地址\r\n    \"Status\": \"2\",//状态 0-全部 1-已超期 2-未超期\r\n\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjQyMTAwLCJleHAiOjE3MzkyNDkzMDAsImlhdCI6MTczOTI0MjEwMH0.lTKjErjB4bfPiM43Rp1KwPkRLgbvXkdXixg1CFIWdw8", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "水电表详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Meter/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON>er", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\": \"063e14a0-63cd-4bf9-896e-5371ee124a04\" //主键编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjQzNDY1LCJleHAiOjE3MzkyNTA2NjUsImlhdCI6MTczOTI0MzQ2NX0.h5c0aVPYWapKARRGH8QoNTpYjbz2GJHBV-WVTvTO9y4", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "抄表记录列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Meter/GetPageListByReading", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON>er", "GetPageListByReading"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10,\r\n    \"ReadingTime\": \"2025-01-03~2025-01-05\",//抄表时间\r\n    \"MeterCode\": \"b0333bc1-44cb-426a-9882-7af9797c94e6\",//水电表主键\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjQzNTYzLCJleHAiOjE3MzkyNTA3NjMsImlhdCI6MTczOTI0MzU2M30.nxk4o8b0b2xhmnF6lWePIsSWnrMj8kKA3s105utKInc", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "品质巡检+设备巡检+安防巡逻+保洁监管", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetPageList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetPageList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"\", //项目编号\r\n    \"ProjectName\": \"\", //项目名称\r\n    \"EquipmentName\": \"\", //设备名称\r\n    \"EquipmentCode\": \"\", //设备编号\r\n    \"InspectorUserName\": \"\", //巡检人\r\n    \"CurUserCode\": \"system\", //当前登入人\r\n    \"PageIndex\": 1,\r\n    \"PageSize\":20\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTg1OTA1LCJleHAiOjE3MzkxOTMxMDUsImlhdCI6MTczOTE4NTkwNX0.ZAnIG59x5DjY2cv703x125YT0Vjrn-rYbQDwfOJVBqg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "增加记录", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"add\",\r\n    \"EquipmentCode\": \"305b98d0-1082-47fc-a3c7-222cd8fe5d5e\", //设备编码\r\n    \"InspectorUserCode\": \"system\", //巡检人编号\r\n    \"InspectionContent\": \"测试\", //巡检内容\r\n    \"Images\": \"/upload/20250211090409.jpg,/upload/20250211090409.jpg\", //巡检图片\r\n    \"VideoPath\": \"/upload/20250211090409.jpg,/upload/20250211090409.jpg\", //巡检视频\r\n    \"LatLon\": \"123,1234\", //经纬度信息\r\n    \"SignImage\": \"/upload/20250211090409.jpg\", //签名\r\n    \"ModuleName\": \"设备巡检\", //所属模块\r\n    \"InspectionRemarks\": [\r\n        {\r\n            \"Content\": \"测试(正常)\",\r\n            \"Result\": \"1\",\r\n            \"FactValue\": \"1\",\r\n        },\r\n         {\r\n            \"Content\": \"检查环境(正常)\",\r\n            \"Result\": \"2\",\r\n            \"FactValue\": \"2\",\r\n        }\r\n    ]\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjcxNTkwLCJleHAiOjE3MzkyNzg3OTAsImlhdCI6MTczOTI3MTU5MH0.Gwahz3wyB1W8PpphD8uuSUPpU8qKnUrom3cesQFzfR4", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "记录详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetInspectionEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetInspectionEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"1\"\r\n\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjYwNDcxLCJleHAiOjE3MzkyNjc2NzEsImlhdCI6MTczOTI2MDQ3MX0.5OHo4qJHeNukwOFdyKpGiFbud1amm397SRVjecMSVrs", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "隐患和待巡检数量统计", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetCountYcAndCq", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetCountYcAndCq"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTg1OTA1LCJleHAiOjE3MzkxOTMxMDUsImlhdCI6MTczOTE4NTkwNX0.ZAnIG59x5DjY2cv703x125YT0Vjrn-rYbQDwfOJVBqg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "分类信息", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetEquipmentCateBizQuery", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetEquipmentCateBizQuery"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"\", //项目编号\r\n    \"CurUserCode\":\"15100000001\" //当前登入人编号\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTg1OTA1LCJleHAiOjE3MzkxOTMxMDUsImlhdCI6MTczOTE4NTkwNX0.ZAnIG59x5DjY2cv703x125YT0Vjrn-rYbQDwfOJVBqg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "位置信息", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetEquipmentLocation", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetEquipmentLocation"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"\", //项目编号\r\n    \"CurUserCode\":\"15100000001\" //当前登入人编号\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTg1OTA1LCJleHAiOjE3MzkxOTMxMDUsImlhdCI6MTczOTE4NTkwNX0.ZAnIG59x5DjY2cv703x125YT0Vjrn-rYbQDwfOJVBqg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "历史记录", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetPageList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetPageList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"\", //项目编号\r\n    \"ProjectName\": \"\", //项目名称\r\n    \"EquipmentName\": \"\", //设备名称\r\n    \"EquipmentCode\": \"9f452f91-5063-49f4-8be4-af24b1a15c72\", //设备编号\r\n    \"InspectorUserName\": \"\", //巡检人\r\n    \"CurUserCode\": \"system\", //当前登入人\r\n       \"PageIndex\": 1,\r\n    \"PageSize\":20\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTg1OTA1LCJleHAiOjE3MzkxOTMxMDUsImlhdCI6MTczOTE4NTkwNX0.ZAnIG59x5DjY2cv703x125YT0Vjrn-rYbQDwfOJVBqg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "巡检标准", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/inspection/GetEquipmentRemarkEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "inspection", "GetEquipmentRemarkEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"EquipmentCode\":\"de63904e-3d27-4359-b9de-da496dfc6cfb\", //设备编号\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTg1OTA1LCJleHAiOjE3MzkxOTMxMDUsImlhdCI6MTczOTE4NTkwNX0.ZAnIG59x5DjY2cv703x125YT0Vjrn-rYbQDwfOJVBqg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "认证", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "GET", "url": {"raw": "http://localhost:5000/api/Auth/Token?appId=pms&appSecret=pms", "host": ["http"], "query": [{"key": "appId", "value": "pms"}, {"key": "appSecret", "value": "pms"}], "variable": [], "path": ["localhost:5000", "api", "<PERSON><PERSON>", "Token"]}, "header": [], "body": {"mode": "raw"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "通知公告", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Notice/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Notice", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\": \"0ac0fe81-672f-4bad-a212-edfa649d4b30\" //主键编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY2ODc0LCJleHAiOjE3MzkxNzQwNzQsImlhdCI6MTczOTE2Njg3NH0.t2wqCQqzV3cGZg4f8-UHl-FUimh0CExGIcVZfBZvzLs", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "设置已读", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Notice/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Notice", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n   \"DoCmd\": \"isread\",\r\n   \"Code\": \"0ac0fe81-672f-4bad-a212-edfa649d4b30\" //主键编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY3Mjk2LCJleHAiOjE3MzkxNzQ0OTYsImlhdCI6MTczOTE2NzI5Nn0.uSbLlDZ5cjxXpyelmTjRRPLJyFEgPppcW5GKbxwUzN0", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "删除公告", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Notice/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Notice", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n   \"DoCmd\": \"delete\",\r\n   \"Code\": \"0ac0fe81-672f-4bad-a212-edfa649d4b31\" //主键编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY3NTQyLCJleHAiOjE3MzkxNzQ3NDIsImlhdCI6MTczOTE2NzU0Mn0.d8nljRth12MMoNPAQdof07EL5TlShzsvjzw5QoxEFvc", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "已读未读数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Notice/GetAllByRead", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Notice", "GetAllByRead"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"UserCode\": \"18361769116\",//用户编码\r\n    \r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY3MDY3LCJleHAiOjE3MzkxNzQyNjcsImlhdCI6MTczOTE2NzA2N30.19Kpj9Q_cEejfl7j3jN0mfUkYHK0AnLGULKF8q5HeT4", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Notice/GetPageList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Notice", "GetPageList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10,\r\n    \"UserCode\": \"18361769116\",//用户编码\r\n    \"IsSee\": \"\",//是否查看 0-否 1- 是\r\n    \"ProjectCode\": \"\",//项目编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc0OTI2LCJleHAiOjE3MzkxODIxMjYsImlhdCI6MTczOTE3NDkyNn0.x8caCpFYrWrCWW5z9dfWJggOxQ2b_W6HLAK1GKEVeUA", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "用户", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "业主认证(姓名+项目)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Login/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\": \"yzcheck\",\r\n    \"CellPhone\": \"15996697224\",\r\n    \"UserName\": \"15\",\r\n    \"ProjectCode\": \"45\",\r\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "登录注册(手机号+验证码)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Login/Check", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "Check"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"CellPhone\": \"17851333817\",\r\n    \"UserType\": \"1\", //1-业主 2-员工\r\n    \"VerifyCode\": \"1111\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc2MTM1LCJleHAiOjE3MzkxODMzMzUsImlhdCI6MTczOTE3NjEzNX0.jBLf2zLCKFUtThOGmG9KbIEqs_rH9ZhNan1yqXUzHew", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "获取用户详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/user/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "user", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"CellPhone\": \"17851333877\" //手机号\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjM2ODg1LCJleHAiOjE3MzkyNDQwODUsImlhdCI6MTczOTIzNjg4NX0.HbGNHpLZvvCDeZnI9LwwAQaGtqNjazqiwfSwqyJRZFo", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "解绑openid", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/user/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "user", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\": \"cancelwxid\",\r\n    \"Code\":\"18361066598\", //用户编号\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjgyLCJleHAiOjE3MzkxODU0ODIsImlhdCI6MTczOTE3ODI4Mn0.NeCxHNKXQUJIK6QOnI2hhBuxEwok7wZ4Ad_X82ABtho", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "绑定openid", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Login/SetOpenId", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "SetOpenId"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"UserCode\": \"\",\r\n    \"OpenId\": \"\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc2MTM1LCJleHAiOjE3MzkxODMzMzUsImlhdCI6MTczOTE3NjEzNX0.jBLf2zLCKFUtThOGmG9KbIEqs_rH9ZhNan1yqXUzHew", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "更换手机号(手机号+验证码)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/user/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "user", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\": \"modifycellPhone\",\r\n    \"Code\":\"18361066598\", //用户编号\r\n    \"CellPhone\": \"18361066598\", //真实姓名\r\n    \"Verification\":\"123\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjgyLCJleHAiOjE3MzkxODU0ODIsImlhdCI6MTczOTE3ODI4Mn0.NeCxHNKXQUJIK6QOnI2hhBuxEwok7wZ4Ad_X82ABtho", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "获取用户角色变化", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "员工认证(姓名+员工号+项目)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Login/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\": \"ygcheck\",\r\n    \"CellPhone\": \"15996697224\",\r\n    \"UserName\": \"15\",\r\n    \"ProjectCode\": \"45\",\r\n    \"JobNumber\": \"45\"\r\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "获取openid", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Login/GetOpenId", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "GetOpenId"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"JsCode\": \"\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc2MTM1LCJleHAiOjE3MzkxODMzMzUsImlhdCI6MTczOTE3NjEzNX0.jBLf2zLCKFUtThOGmG9KbIEqs_rH9ZhNan1yqXUzHew", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "获取验证码", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "修改用户头像、姓名、性别", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/user/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "user", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\": \"modify\",\r\n    \"Code\":\"18796525781\", //用户编号\r\n    \"UserName\": \"王小发\", //真实姓名\r\n    \"Gender\": \"女\",//性别\r\n    \"JobNumber\": \"12321312\", //员工工号\r\n    \"HeadImg\": \"/upload/20250210170325.jpg\", //头像\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTc4MjgyLCJleHAiOjE3MzkxODU0ODIsImlhdCI6MTczOTE3ODI4Mn0.NeCxHNKXQUJIK6QOnI2hhBuxEwok7wZ4Ad_X82ABtho", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "通讯录", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Contact/UserList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Contact", "UserList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10,\r\n    \"UserName\": \"综管\",//用户名称\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY3NjU1LCJleHAiOjE3MzkxNzQ4NTUsImlhdCI6MTczOTE2NzY1NX0.o3dcIg_kiJgC2SQrLgVtP0SjR8BuEsgcoX_s7zWmwZc", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "项目", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "项目详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\": \"4c7b589b-7c98-4e56-8af3-deecef14e9ea\"//项目编码\r\n    \r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjYxOTY1LCJleHAiOjE3MzkyNjkxNjUsImlhdCI6MTczOTI2MTk2NX0.5aLDcOJCup2HHZHzYzOXTAnULe11QjtSOvDsRXfQywU", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "能耗管理数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountOverMeterReading", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountOverMeterReading"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"5d0d1ad4-5d89-4486-b8d7-c00bd762a94b\", //项目编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY3Nzc3LCJleHAiOjE3MzkyNzQ5NzcsImlhdCI6MTczOTI2Nzc3N30.Kr-OsUiplsYYfmsUfuPPwkyVXjZXnBbvPpsCTHtX3-I", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "日报数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountDaily", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "<PERSON><PERSON><PERSON><PERSON>"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b64e9736-9b8d-4fd4-aa10-11cc38cd1b07\", //项目编码\r\n    \"UserCode\": \"7325f533-2d4d-4bfe-9fac-e42353960090\"\r\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "维修工单数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountRepair", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "Count<PERSON><PERSON><PERSON>"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b64e9736-9b8d-4fd4-aa10-11cc38cd1b07\", //项目编码\r\n    \"UserCode\": \"18796525719\"\r\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "包厢预定数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountBoxApply", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountBoxApply"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b577c74b-1973-4a58-ab61-c43d0b9f7a0e\", //项目编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY4NzY0LCJleHAiOjE3MzkyNzU5NjQsImlhdCI6MTczOTI2ODc2NH0.UEVDxTMID5C7Em8tnp4rnby4EJ-BjaJFWVlhIJ_k8Yo", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "安全检查超期数量/质量检查/网格巡检/安全检查/质量检查/网格巡检数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountOverInspection", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountOverInspection"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b577c74b-1973-4a58-ab61-c43d0b9f7a0e\", //项目编码\r\n    \"ModuleName\": \"质量检查\", //模块名称(设备巡检，安防巡逻，保洁监管，安全检查，质量检查，网格巡检)\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY3Nzc3LCJleHAiOjE3MzkyNzQ5NzcsImlhdCI6MTczOTI2Nzc3N30.Kr-OsUiplsYYfmsUfuPPwkyVXjZXnBbvPpsCTHtX3-I", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "我的项目列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/MyProJectList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "MyProJectList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"UserCode\": \"15100000001\", //用户编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjU5NjAxLCJleHAiOjE3MzkyNjY4MDEsImlhdCI6MTczOTI1OTYwMX0.rhtesnfWcQ0gp-xWUWiNN1Muss2QQe-RR4wKoyIcd7Y", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "资产管理数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountAsset", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountAsset"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b64e9736-9b8d-4fd4-aa10-11cc38cd1b07\", //项目编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY4NzY0LCJleHAiOjE3MzkyNzU5NjQsImlhdCI6MTczOTI2ODc2NH0.UEVDxTMID5C7Em8tnp4rnby4EJ-BjaJFWVlhIJ_k8Yo", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "所有项目列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/GetAllProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "GetAllProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjYxMTE2LCJleHAiOjE3MzkyNjgzMTYsImlhdCI6MTczOTI2MTExNn0.W9rEPmQetHxrdauY_a66OrF8sVc7wDmP9cs20R_wPRw", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "卤菜外卖数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountTakeOutApply", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountTakeOutApply"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b577c74b-1973-4a58-ab61-c43d0b9f7a0e\", //项目编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY4NzY0LCJleHAiOjE3MzkyNzU5NjQsImlhdCI6MTczOTI2ODc2NH0.UEVDxTMID5C7Em8tnp4rnby4EJ-BjaJFWVlhIJ_k8Yo", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "项目详情菜单权限", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/GetAllMenu", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "GetAllMenu"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"UserCode\": \"15100000001\", //用户编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY1MTAyLCJleHAiOjE3MzkyNzIzMDIsImlhdCI6MTczOTI2NTEwMn0.lem8SsWdOnMQlslspXFnVh9bZ3R8pXZ2vBJDV6WS6ug", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "会议预约数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountMeeting", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountMeeting"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b64e9736-9b8d-4fd4-aa10-11cc38cd1b07\", //项目编码\r\n    \"UserCode\": \"7325f533-2d4d-4bfe-9fac-e42353960090\"\r\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "餐食预留数量", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/CountReserveApply", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "CountReserveApply"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b577c74b-1973-4a58-ab61-c43d0b9f7a0e\", //项目编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY4NzY0LCJleHAiOjE3MzkyNzU5NjQsImlhdCI6MTczOTI2ODc2NH0.UEVDxTMID5C7Em8tnp4rnby4EJ-BjaJFWVlhIJ_k8Yo", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "餐食预留", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "评价", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"evaluate\",\r\n    \"Code\":\"98a8835d-688b-4537-90be-22c201cd9a17\",\r\n    \"EvaluateLevel\":\"5\",\r\n    \"EvaluateContent\":\"nice\",\r\n    \"EvaluatePhotos\":\"/upload/...\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"check\",\r\n    \"Code\":\"98a8835d-688b-4537-90be-22c201cd9a17\",\r\n    \"ApplyStatus\":\"1\",//1通过，2不通过\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CheckRemark\":\"-\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "确认取餐", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"complete\",\r\n    \"Code\":\"98a8835d-688b-4537-90be-22c201cd9a17\",\r\n    \"GlobalUserCode\":\"15996697224\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"98a8835d-688b-4537-90be-22c201cd9a17\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/GetPageListByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "GetPageListByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"ApplyStatusSearch\":\"待处理\",//待处理，待用餐，已完成\r\n    \"ReserveOrderNo\":\"\",\r\n    \"PageIndex\":1,\r\n    \"PageSize\":10\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5Njk1LCJleHAiOjE3MzkyNzY4OTUsImlhdCI6MTczOTI2OTY5NX0.0-yIqBlI8Zv37cG9bbA5WUTrHssrGBvrPGr5TsAcpfg", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "取消", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"cancel\",\r\n    \"Code\":\"98a8835d-688b-4537-90be-22c201cd9a17\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "审核历史", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/api/FoodReservationMngApi/GetDetail?Docmd=history", "host": ["api"], "query": [{"key": "Docmd", "value": "history"}], "variable": [], "path": ["FoodReservationMngApi", "GetDetail"]}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"add\",\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"ReserveTime\":\"2025-02-10 20:00:00\",\r\n    \"Number\":\"2\",\r\n    \"Remark\":\"-\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "修改", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Reserve/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Reserve", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"modify\",\r\n    \"Code\":\"98a8835d-688b-4537-90be-22c201cd9a17\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"ReserveTime\":\"2025-02-10 20:00:00\",\r\n    \"Number\":\"1\",\r\n    \"Remark\":\"-\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTgyOTA0LCJleHAiOjE3MzkxOTAxMDQsImlhdCI6MTczOTE4MjkwNH0.Q2GtrflP5x_c2g50DE5CUZ0GNpcVWLmc0eR2pCPTfZk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "cms", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "", "host": [], "query": [], "variable": []}, "header": [], "body": {"mode": "none", "raw": "{}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "资产信息", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Asset/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n   \"DoCmd\": \"add\",\r\n   \"AssetName\": \"资产名称0001\",//资产名称\r\n   \"AssetCate\": \"资产类型11\",//资产类型\r\n   \"Brand\": \"测试品牌\",//品牌\r\n   \"Specification\": \"测试规格型号\",//规格型号\r\n   \"BuyDate\": \"2025-06-09\",//购买日期\r\n   \"IsUse\": \"1\",//使用状态\r\n   \"Remark\": \"测试备注\",//备注\r\n   \"AssetPic\": \"/upload/20250209011943.png\",//图片\r\n   \"ProjectCode\": \"f1f9f854-05ac-42c4-b09d-d076363f624d\",//项目编码\r\n   \"UserCode\": \"18361769116\",//用户编码\r\n\r\n\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY4NjU1LCJleHAiOjE3MzkxNzU4NTUsImlhdCI6MTczOTE2ODY1NX0._erGauiUFnjMeQzLFYc1T5idYYAsmfjGGHggaZY0PNs", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "修改", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Asset/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n   \"DoCmd\": \"modify\",\r\n     \"Code\": \"f343b831-f046-47f5-9b6b-bc8aa40e7b76\",//编码\r\n   \"AssetName\": \"资产名称0002\",//资产名称\r\n   \"AssetCate\": \"资产类型22\",//资产类型\r\n   \"Brand\": \"测试品牌\",//品牌\r\n   \"Specification\": \"测试规格型号\",//规格型号\r\n   \"BuyDate\": \"2025-06-09\",//购买日期\r\n   \"IsUse\": \"1\",//使用状态\r\n   \"Remark\": \"测试备注\",//备注\r\n   \"AssetPic\": \"/upload/20250209011943.png\",//图片\r\n   \"ProjectCode\": \"f1f9f854-05ac-42c4-b09d-d076363f624d\",//项目编码\r\n   \"UserCode\": \"18361769116\",//用户编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY5MDAxLCJleHAiOjE3MzkxNzYyMDEsImlhdCI6MTczOTE2OTAwMX0.9D6p2oU4JE0L5YhwFdI9v5nYtjqvk1Krd5sAxOvkqnk", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Asset/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\": \"ce9ff3cd-3c65-454a-b4ce-ec5eb8d37b16\" //主键编码\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MTY4Mjk3LCJleHAiOjE3MzkxNzU0OTcsImlhdCI6MTczOTE2ODI5N30.PtYfaVmWf9EHCQeHxbQq-k7nUFa1NMeuoxAsRbIv81w", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Asset/GetPageList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "<PERSON><PERSON>", "GetPageList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10,\r\n    \"AssetNameorNum\": \"443836\",//关键字查询名称和编号\r\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "我的预约", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "列表(会议+外卖+餐食+包厢)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/GetYyPageListByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "GetYyPageListByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"b64e9736-9b8d-4fd4-aa10-11cc38cd1b07\",\r\n    \"RepairStatusSearch\": \"预约成功\", //预约成功、预约失败、待审核、已完成\r\n    \"ApplyUserCode\":\"80ecbb21-5a82-48e5-b248-02d04a1dfc18\",\r\n    \"RepairOrderNo\": \"\",\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "报修", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "申请详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/GetEntity", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "GetEntity"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"4bb1ef73-dfe3-4728-8e09-5e446e71dad8\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "维修类型", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/GetDataDicList", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "GetDataDicList"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Code\":\"RepairType\"\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "确认接单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"receive\",\r\n    \"Code\":\"4bb1ef73-dfe3-4728-8e09-5e446e71dad8\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "我的报修", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Project/GetRepairPageListByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Project", "GetRepairPageListByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\": \"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"RepairStatusSearch\": \"已完成\", //待维修，待评价，已完成\r\n    \"ApplyUserCode\":\"15996697224\",\r\n    \"RepairOrderNo\": \"\",\r\n    \"PageIndex\": 1,\r\n    \"PageSize\": 10\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "催单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"reminder\",\r\n    \"Code\":\"4bb1ef73-dfe3-4728-8e09-5e446e71dad8\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "确认完成", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"complete\",\r\n    \"Code\":\"4bb1ef73-dfe3-4728-8e09-5e446e71dad8\",\r\n    \"LatLon\":\"1,1\",//经纬度\r\n    \"ServiceList\":\"../upload/1.png\",//维修服务单\r\n    \"RepairContent\":\"完成\",//维修备注\r\n    \"RepairPhotos\":\"../upload/2.png\",//维修图片\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "派单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"check\",\r\n    \"Code\":\"4bb1ef73-dfe3-4728-8e09-5e446e71dad8\",\r\n    \"GlobalUserCode\":\"80ecbb21-5a82-48e5-b248-02d04a1dfc18\",//派单人\r\n    \"RepairType\":\"给排水系统\",\r\n    \"RepairUserCode\":\"80ecbb21-5a82-48e5-b248-02d04a1dfc18\"//维修人\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "取消", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"cancel\",\r\n    \"Code\":\"47ca507e-003d-4b75-9671-0a749b14f4f9\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "评价", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"evaluate\",\r\n    \"Code\":\"4bb1ef73-dfe3-4728-8e09-5e446e71dad8\",\r\n    \"EvaluateLevel\":\"5\",\r\n    \"EvaluateContent\":\"nice\",\r\n    \"EvaluatePhotos\":\"/upload/...\",\r\n    \"RepairQuality\":\"满意\",//维修质量:满意/基本满意/不满意\r\n    \"ServiceQuality\":\"满意\"//服务质量:满意/不满意\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "维修服务人员列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/GetRepairUserByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "GetRepairUserByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "申请列表", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/GetPageListByProject", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "GetPageListByProject"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"GlobalUserCode\":\"80ecbb21-5a82-48e5-b248-02d04a1dfc18\",\r\n    \"RepairStatusSearch\":\"进行中\",//待指派、待接单、已接单/进行中、已完成\r\n    \"RepairOrderNo\":\"\",\r\n    \"PageIndex\":1,\r\n    \"PageSize\":10\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "http://localhost:5000/api/PMSWebApi/Repair/Execute", "host": ["http"], "query": [], "variable": [], "path": ["localhost:5000", "api", "PMSWebApi", "Repair", "Execute"]}, "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"DoCmd\":\"add\",\r\n    \"ProjectCode\":\"f1f9f854-05ac-42c4-b09d-d076363f624d\",\r\n    \"GlobalUserCode\":\"15996697224\",\r\n    \"CellPhone\":\"15996697224\",\r\n    \"Address\":\"10楼\",\r\n    \"RepairMatter\":\"灯不亮\",\r\n    \"Photos\":\"\",\r\n}"}, "auth": {"type": "bearer", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMjMiLCJuYW1lIjoicG1zIiwibmJmIjoxNzM5MjY5ODQ0LCJleHAiOjE3MzkyNzcwNDQsImlhdCI6MTczOTI2OTg0NH0.bG2zDWuuLof3koF2y3xotls8VDFooR3Qw2K3nI9_B0o", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}]}