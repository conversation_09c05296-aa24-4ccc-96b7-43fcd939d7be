<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="能耗管理" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">详情记录</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">抄表日期</view>
          <view class="flex-bd">{{ form.ReadingTime }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">水电表类型</view>
          <view class="flex-bd">{{ form.MeterType }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">表号</view>
          <view class="flex-bd">{{ form.MeterNumber }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">表数值</view>
          <view class="flex-bd">{{ form.MeterValue }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.Remark }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      Code: "",
      fileList1: [],
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.$apis.getEnergyRecordDetail({ Code: this.Code }).then((res) => {
        if (res.data.Images) {
          this.fileList1 = res.data.Images.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        }
        this.form = res.data;
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
