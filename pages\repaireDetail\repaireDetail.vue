<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">上报信息</view>
        <view class="status status1" v-if="form.RepairStatus == 0">{{ form.RepairStatusCaption }}</view>
        <view class="status status3" v-else-if="form.RepairStatus == 2">{{ form.RepairStatusCaption }}</view>
        <view class="status status2" v-else>{{ form.RepairStatusCaption }}</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">单号</view>
          <view class="flex-bd">{{ form.RepairOrderNo }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">上报时间</view>
          <view class="flex-bd">{{ form.ApplyTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">上报类型</view>
          <view class="flex-bd">{{ form.ApplyTypeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">上报人</view>
          <view class="flex-bd">{{ form.ApplyUserName }}</view>
        </view>
        <view class="form-item flex" @click="$c.callPhone(form.CellPhone)">
          <view class="label">联系电话</view>
          <view class="flex-bd">{{ form.CellPhone }}</view>
          <view class="flex-ft">
            <u-icon name="phone" :size="20" color="#999"></u-icon>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">上报地点</view>
          <view class="flex-bd">{{ form.Address }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">上报内容</view>
          <view class="flex-bd">{{ form.RepairMatter }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>
    <block v-if="form.RepairStatus == 0 && type == 'apply'">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">派单信息</view>
        </view>
        <view class="form" style="padding: 10rpx 0">
          <u-form labelWidth="80">
            <u-form-item label="维修类型" borderBottom @click="repairTypeShow = true">
              <u-input border="none" v-model="dispatchForm.RepairType" disabled disabledColor="#fff" placeholder="请选择" />
              <u-icon slot="right" name="arrow-right"></u-icon>
            </u-form-item>
            <u-form-item label="维修人员" @click="$c.selectPeople('repairService', 1, form.ProjectCode, ['RepairUserName', 'RepairUserCode'])">
              <u-input border="none" v-model="dispatchForm.RepairUserName" disabled disabledColor="#fff" placeholder="请选择" />
              <u-icon slot="right" name="arrow-right"></u-icon>
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="mr30">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="dispatch">确认派单</u-button>
      </view>
    </block>

    <view class="form-pannel" v-if="form.RepairStatus > 0">
      <view class="pannel-title bor-b">
        <view class="title-content">派单信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">处理类型</view>
          <view class="flex-bd">{{ form.RepairType }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">处理人</view>
          <view class="flex-bd">{{ form.RepairUserName }}</view>
        </view>
        <view class="form-item flex" v-if="form.RepairStatus > 3">
          <view class="label">完成时间</view>
          <view class="flex-bd">{{ form.CompleteTimeCaption }}</view>
        </view>
      </view>
    </view>

    <view class="mr30" v-if="form.RepairStatus == 1 && form.RepairUserCode == UserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="confirm">接单</u-button>
    </view>

    <block v-if="form.RepairStatus > 2 && dealList.length > 0">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">处理结果</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="deal-list">
            <view class="item flext" v-for="(item, index) in dealList">
              <view class="icon">
                <u-icon v-if="index > dealList.length - 2 && form.RepairStatus > 3" name="checkmark" :size="12" color="#fff"></u-icon>
                <u-icon v-else name="more-dot-fill" :size="12" color="#fff"></u-icon>
              </view>
              <view class="con flex-bd">
                <view class="time">{{ item.DealTime }}</view>
                <view class="content">{{ item.DealRemark }}</view>
                <view class="imgs" v-if="item.DealImgs.length > 0">
                  <u-upload width="30" height="30" :maxCount="item.DealImgs.length" :fileList="item.DealImgs" :disabled="true" :deletable="false"></u-upload>
                </view>
              </view>
              <view class="line"></view>
            </view>
          </view>
        </view>
      </view>
    </block>

    <block v-if="form.RepairStatus > 3 && checkDetail">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">质量检查</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="form-item flex">
            <view class="label">检查备注</view>
            <view class="flex-bd">{{ checkDetail.CheckRemark }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">检查人</view>
            <view class="flex-bd">{{ checkDetail.CheckUserName }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">检查时间</view>
            <view class="flex-bd">{{ checkDetail.CheckTime }}</view>
          </view>
          <view class="form-item flex" v-if="fileList5.length > 0">
            <view class="label">图片</view>
            <view class="flex-bd">
              <u-upload name="1" multiple :maxCount="fileList5.length" :fileList="fileList5" :disabled="true" :deletable="false"></u-upload>
            </view>
          </view>
        </view>
      </view>
    </block>

    <block v-if="form.RepairStatus == 3 && UserCode == form.RepairUserCode">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">处理信息</view>
        </view>
        <view class="form" style="padding: 10rpx 0">
          <u-form labelWidth="80">
            <u-form-item required label="处理状态" borderBottom>
              <u-radio-group v-model="repairForm.DealStatus">
                <u-radio :activeColor="$c.color()" :customStyle="{ marginRight: '30rpx' }" v-for="(item, index) in dealStatusList" :key="index" :label="item.label" :name="item.name"></u-radio>
              </u-radio-group>
            </u-form-item>
            <u-form-item required label="备注" borderBottom>
              <u-textarea v-model="repairForm.DealRemark" border="none" placeholder="请输入备注" />
            </u-form-item>
            <u-form-item label="处理图片">
              <my-upload v-model="RepairPhotos" @change="repairPhotosChange"></my-upload>
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="mr30">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="complete">提交</u-button>
      </view>
    </block>

    <view class="form-pannel" v-if="form.EvaluateTimeCaption">
      <view class="pannel-title bor-b">
        <view class="title-content">评价信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">评价星级</view>
          <view class="flex-bd">
            <u-rate :count="form.EvaluateLevel" v-model="form.EvaluateLevel"></u-rate>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">维修质量</view>
          <view class="flex-bd">{{ form.RepairQuality }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">服务质量</view>
          <view class="flex-bd">{{ form.ServiceQuality }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">评价时间</view>
          <view class="flex-bd">{{ form.EvaluateTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">评价内容</view>
          <view class="flex-bd">{{ form.EvaluateContent }}</view>
        </view>
        <view class="form-item flex" v-if="fileList3.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList3.length" :fileList="fileList3" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>

    <block v-if="form.RepairStatus > 3 && UserCode == form.DispatchUserCode && !checkDetail">
      <view class="form-pannel">
        <view class="pannel-title bor-b">
          <view class="title-content">质量检查</view>
        </view>
        <view class="form" style="padding: 10rpx 0">
          <u-form labelWidth="80">
            <u-form-item required label="检查备注" borderBottom>
              <u-textarea v-model="checkForm.CheckRemark" border="none" placeholder="请输入检查备注" />
            </u-form-item>
            <u-form-item label="处理图片">
              <my-upload v-model="checkPhotos" @change="checkPhotosChange"></my-upload>
            </u-form-item>
          </u-form>
        </view>
      </view>
      <view class="mr30">
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="addCheck">提交</u-button>
      </view>
    </block>

    <view class="mr30" v-if="form.RepairStatus == 1 && UserInfo.CurRolesCaption.split(',').includes('维修服务负责人')">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="remind">催单</u-button>
    </view>

    <view class="mr30" v-if="form.RepairStatus == 0 && UserCode == form.ApplyUserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="cancel">取消</u-button>
    </view>
    <view class="mr30" v-if="form.RepairStatus == 4 && UserCode == form.ApplyUserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="$c.evaluate(Code, 'repair')">评价</u-button>
    </view>

    <view style="height: 1px"></view>
    <u-picker :show="repairTypeShow" :columns="repairTypeColumns" keyName="label" @cancel="repairTypeShow = false" @confirm="repairTypeConfirm"></u-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserCode: uni.getStorageSync("UserCode"),
      UserInfo: uni.getStorageSync("UserInfo"),
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
      checkPhotos: [],
      ServiceList: [],
      RepairPhotos: [],
      value: 5,
      Code: "", //单据code
      form: {},
      type: "",
      dispatchForm: {
        Code: "",
        GlobalUserCode: uni.getStorageSync("UserCode"), //派单人
        RepairType: "",
        RepairUserCode: "", //维修人
        RepairUserName: "",
      },
      repairForm: {
        RepairCode: "", //报事报修单编号
        DealUserCode: uni.getStorageSync("UserCode"), //处理人编码
        DealStatus: "", //处理状态（0处理中 1已完成）
        DealRemark: "", //处理备注
        DealImgs: "", //现场拍照
        DealTime: "", //处理时间
        CreateUserCode: uni.getStorageSync("UserCode"), //登录人
      },
      checkForm: {
        RepairCode: "", //报事报修单编号
        CheckUserCode: uni.getStorageSync("UserCode"), //检查人编码
        CheckTime: "", //检查时间
        CheckRemark: "", //检查备注
        CheckImgs: "", //图片
      },
      repairTypeShow: false,
      repairTypeColumns: [],
      dealStatusList: [
        { label: "处理中", name: "0" },
        { label: "已完成", name: "1" },
      ],
      dealList: [],
      checkDetail: "",
    };
  },
  onShow() {
    uni.$on("selectPeople", (data) => {
      this.dispatchForm.RepairUserName = data.list[0].UserName;
      this.dispatchForm.RepairUserCode = data.list[0].Code;
      uni.$off("selectPeople");
    });
    uni.$on("evaluateRefresh", (res) => {
      if (res) {
        this.getDetail();
      }
      uni.$off("evaluateRefresh");
    });
  },
  onLoad(options) {
    console.log(options);
    this.Code = options.Code;
    this.type = options.type;
    this.dispatchForm.Code = this.Code;
    this.repairForm.RepairCode = this.Code;
    this.getDetail();
    if (this.type == "apply") {
      this.$apis
        .getRepairTypeList({
          Code: "RepairType",
        })
        .then((res) => {
          console.log(res);
          var columns = res.data.map((item) => ({
            label: item.DataDicName,
            value: item.DataDicName,
          }));
          this.repairTypeColumns = [columns];
        });
      if (this.UserInfo.CurRolesCaption.split(",").includes("维修服务人员") && this.form.RepairStatus == 3) {
        uni.getLocation({
          type: "gcj02",
          success: (res) => {
            this.repairForm.LatLon = res.latitude + "," + res.longitude;
          },
          fail: (err) => {
            console.log("获取位置失败", err);
            this.$u.toast("获取位置失败,请检查定位权限是否开启");
          },
        });
      }
    }
  },
  methods: {
    addCheck() {
      if (this.checkForm.CheckRemark == "") {
        this.$u.toast("请输入检查备注");
        return;
      }
      this.checkForm.CheckTime = uni.$u.timeFormat(new Date(), "yyyy-mm-dd hh:MM:ss");
      this.checkForm.RepairCode = this.Code;
      this.$apis.checkRepair(this.checkForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "提交成功", icon: "success" });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
        } else {
          this.$u.toast(res.msg);
        }
      });
    },
    checkPhotosChange(data) {
      this.checkForm.CheckImgs = data.urls;
    },
    serviceListChange(data) {
      this.repairForm.ServiceList = data.urls;
    },
    repairPhotosChange(data) {
      this.repairForm.DealImgs = data.urls;
    },
    repairTypeConfirm(e) {
      console.log(e);
      this.dispatchForm.RepairType = e.value[0].value;
      this.repairTypeShow = false;
    },
    getDetail() {
      this.$apis
        .getRepairDetail({
          Code: this.Code,
        })
        .then((res) => {
          if (res.data.Detail && res.data.Detail.length > 0) {
            var checkDetail = res.data.Detail[0];
            checkDetail.CheckTime = checkDetail.CheckTime.replace("T", " ");
            this.checkDetail = checkDetail;
            this.fileList5 = checkDetail.CheckImgs.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          this.form = res.data.RepairEntity;
          if (res.data.RepairEntity.Photos) {
            this.fileList1 = res.data.RepairEntity.Photos.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          if (res.data.RepairEntity.RepairPhotos) {
            this.fileList2 = res.data.RepairEntity.RepairPhotos.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          if (res.data.RepairEntity.EvaluatePhotos) {
            this.fileList3 = res.data.RepairEntity.EvaluatePhotos.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
          if (res.data.RepairEntity.ServiceList) {
            this.fileList4 = res.data.RepairEntity.ServiceList.split(",").map((url) => ({
              url: this.$http.config.staticURL + url,
            }));
          }
        });
      this.$apis
        .getRepairRecordList({
          RepairCode: this.Code,
        })
        .then((res) => {
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].DealImgs) {
              res.data[i].DealImgs = res.data[i].DealImgs.split(",").map((url) => ({
                url: this.$http.config.staticURL + url,
              }));
            }
            res.data[i].DealTime = res.data[i].DealTime.replace("T", " ").split(".")[0];
          }
          this.dealList = res.data;
        });
    },
    remind() {
      this.$apis
        .remindRepair({
          Code: this.Code,
        })
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "催单成功", icon: "success" });
            setTimeout(() => {
              this.getDetail();
            }, 1500);
          } else {
            this.$u.toast(res.msg);
          }
        });
    },
    complete() {
      if (!this.repairForm.DealStatus) {
        this.$u.toast("请选择处理状态");
        return;
      }
      if (this.repairForm.DealRemark == "") {
        this.$u.toast("请输入处理备注");
        return;
      }
      if (this.repairForm.DealStatus == 1) {
        uni.showModal({
          title: "提示",
          content: "确定完成吗？",
          success: (res) => {
            if (res.confirm) {
              this.submitDeal();
            }
          },
        });
      } else {
        this.submitDeal();
      }
    },
    submitDeal() {
      this.repairForm.DealTime = uni.$u.timeFormat(new Date(), "yyyy-mm-dd hh:MM:ss");
      this.$apis.addRepairRecord(this.repairForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "提交成功", icon: "success" });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
          this.repairForm.DealStatus = "";
          this.repairForm.DealRemark = "";
          this.RepairPhotos = [];
          this.repairForm.DealImgs = "";
        } else {
          this.$u.toast(res.msg);
        }
      });
    },
    confirm() {
      this.$apis
        .confirmRepair({
          Code: this.Code,
        })
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "接单成功", icon: "success" });
            setTimeout(() => {
              this.getDetail();
            }, 1500);
          } else {
            this.$u.toast(res.msg);
          }
        });
    },
    dispatch() {
      if (this.dispatchForm.RepairType == "") {
        this.$u.toast("请选择维修类型");
        return;
      }
      if (this.dispatchForm.RepairUserName == "") {
        this.$u.toast("请选择维修人员");
        return;
      }
      this.$apis.dispatchRepair(this.dispatchForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({ mask: true, title: "派单成功", icon: "success" });
          setTimeout(() => {
            this.getDetail();
          }, 1500);
        } else {
          this.$u.toast(res.msg);
        }
      });
    },
    cancel() {
      uni.showModal({
        title: "提示",
        content: "确定取消吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .cancelRepair({
                Code: this.Code,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "取消成功", icon: "success" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
