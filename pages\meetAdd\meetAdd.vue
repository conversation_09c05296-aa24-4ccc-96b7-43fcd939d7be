<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="会议预约" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">会议信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 申请人 -->
          <u-form-item label="申请人" borderBottom>
            <u-input v-model="form.GlobalUserName" border="none" disabledColor="#fff" :disabled="true" placeholder="申请人" />
          </u-form-item>
          <!-- 联系电话 -->
          <u-form-item required label="联系电话" borderBottom>
            <u-input v-model="form.CellPhone" border="none" placeholder="请输入联系电话" />
          </u-form-item>
          <!-- 会议名称 -->
          <u-form-item required label="会议名称" borderBottom>
            <u-input v-model="form.MeetingName" border="none" placeholder="请输入会议名称" />
          </u-form-item>
          <!-- 会议室 -->
          <u-form-item required label="会议室" borderBottom @click="roomShow = true">
            <u-input v-model="form.ApplyRoomName" border="none" disabled disabledColor="#fff" placeholder="请选择会议室" />
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <!-- 会议时间 -->
          <u-form-item required label="开始时间" borderBottom @click="showTimePicker('start')">
            <u-input v-model="form.ApplyFromTime" border="none" disabled disabledColor="#fff" placeholder="请选择开始时间" />
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item required label="结束时间" borderBottom @click="showTimePicker('end')">
            <u-input v-model="form.ApplyEndTime" border="none" disabled disabledColor="#fff" placeholder="请选择结束时间" />
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <!-- 会议主持人 -->
          <u-form-item label="会议主持人" borderBottom>
            <u-input v-model="form.MeetingHost" border="none" placeholder="请输入会议主持人" />
          </u-form-item>
          <!-- 主席台名单 -->
          <u-form-item label="主席台名单" borderBottom>
            <u-input v-model="form.RostrumList" border="none" placeholder="请输入主席台名单" />
          </u-form-item>
          <!-- 参会人数 -->
          <u-form-item label="参会人数" borderBottom>
            <u-input v-model="form.PersonNumber" border="none" type="number" placeholder="请输入参会人数" />
          </u-form-item>
          <!-- 服务需求 -->
          <u-form-item label="服务需求" borderBottom @click="selectService">
            <u-input v-model="form.Demand" border="none" disabled disabledColor="#fff" placeholder="请选择服务需求" />
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <!-- 备注 -->
          <u-form-item label="备注">
            <u-textarea v-model="form.Remark" border="none" placeholder="请输入备注信息" />
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>

    <!-- 会议室选择器 -->
    <u-picker :show="roomShow" :columns="roomColumns" keyName="label" @cancel="roomShow = false" @confirm="roomConfirm"></u-picker>
    <!-- 时间选择器 -->
    <u-datetime-picker :show="timeShow" v-model="currentTime" @confirm="timeConfirm" @cancel="timeShow = false"></u-datetime-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        ProjectCode: uni.getStorageSync("UserInfo").ProjectCodes,
        GlobalUserCode: uni.getStorageSync("UserCode"),
        GlobalUserName: uni.getStorageSync("UserInfo").UserName,
        CellPhone: uni.getStorageSync("UserInfo").CellPhone,
        ApplyFromTime: "",
        ApplyEndTime: "",
        ApplyRoomCode: "",
        ApplyRoomName: "",
        MeetingName: "",
        MeetingHost: "",
        RostrumList: "",
        PersonNumber: "",
        Demand: "",
        Remark: "",
      },
      roomShow: false,
      roomColumns: [],
      timeShow: false,
      currentTime: Number(new Date()),
      timeType: "",
    };
  },
  onLoad(e) {
    if (e.ProjectCode) {
      this.form.ProjectCode = e.ProjectCode;
    }
    // 获取会议室列表
    this.$apis
      .getMeetingRoomList({
        ProjectCode: this.form.ProjectCode,
      })
      .then((res) => {
        this.roomColumns = [
          res.data.map((item) => ({
            label: item.RoomName,
            value: item.Code,
          })),
        ];
      });
  },
  onShow() {
    // 监听服务选择返回
    uni.$on("selectService", (services) => {
      this.form.Demand = services.join(",");
      uni.$off("selectService");
    });
  },
  methods: {
    roomConfirm(e) {
      this.form.ApplyRoomName = e.value[0].label;
      this.form.ApplyRoomCode = e.value[0].value;
      this.roomShow = false;
    },
    showTimePicker(type) {
      this.timeType = type;
      this.timeShow = true;
    },
    timeConfirm(e) {
      const time = this.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:ss");
      if (this.timeType === "start") {
        this.form.ApplyFromTime = time;
      } else {
        this.form.ApplyEndTime = time;
      }
      this.timeShow = false;
    },
    selectService() {
      uni.navigateTo({
        url: "../selectMeetingService/selectMeetingService",
      });
    },
    formValidation() {
      if (!this.form.MeetingName) {
        this.$u.toast("请输入会议名称");
        return false;
      }
      if (!this.form.ApplyRoomName) {
        this.$u.toast("请选择会议室");
        return false;
      }
      if (!this.form.ApplyFromTime) {
        this.$u.toast("请选择开始时间");
        return false;
      }
      if (!this.form.ApplyEndTime) {
        this.$u.toast("请选择结束时间");
        return false;
      }
      if (!this.form.CellPhone) {
        this.$u.toast("请输入联系电话");
        return false;
      }
      if (!this.$u.test.mobile(this.form.CellPhone)) {
        this.$u.toast("请输入正确的联系电话");
        return false;
      }
      return true;
    },
    submit() {
      if (!this.formValidation()) return;

      this.$apis
        .addMeetingApply(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });
            setTimeout(() => {
              uni.navigateBack();
              uni.$emit("meetRefresh", true);
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
