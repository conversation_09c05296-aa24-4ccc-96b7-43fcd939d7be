$btn-bg: linear-gradient(135deg, #08cc91, #19c2d1);
/* 默认颜色 */
$color-primary: #08cc91;
/* 默认颜色浅色 */
$color-primary-light: #edfef9;
.mr30 {
  margin: 30rpx;
}
.mr20 {
  margin: 20rpx;
}
.mr10 {
  margin: 10rpx;
}
.pd30 {
  padding: 30rpx;
}
.pd20 {
  padding: 20rpx;
}
.pd030 {
  padding: 0 30rpx;
}
.pd300 {
  padding: 30rpx 0;
}
.navigator-hover {
  background: none;
}
.navigator-hover {
  background: transparent;
}
.inline {
  display: inline-block;
}
.hr {
  height: 12rpx;
  background: #f6f6f6;
  width: 100%;
}
.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flext {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-start;
  -webkit-box-align: flex-start;
  -webkit-align-items: flex-start;
}
.flexb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-end;
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
}
.flex-bd {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}
.bor-t,
.bor-b,
.bor-r,
.bor-l {
  position: relative;
}
.bor-t:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid rgb(210, 210, 211);
  color: rgb(210, 210, 211);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.bor-b:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  top: inherit;
  border-bottom: 1px solid rgb(210, 210, 211);
  color: rgb(210, 210, 211);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.bor-r:before {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  left: inherit;
  height: 100%;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
.bor-l:before {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
image {
  vertical-align: middle;
}
input:disabled,
.uni-input-input:disabled {
  pointer-events: none;
}
.tc {
  text-align: center;
}

.line-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.login {
  background-image: url(https://annyou.oss-cn-shenzhen.aliyuncs.com/1/1/img/top-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center bottom;
  height: 400rpx;
}
.login-title {
  text-align: center;
  font-size: 48rpx;
  font-weight: bold;
  padding-top: 220rpx;
}

.login-subtitle {
  text-align: center;
  font-size: 32rpx;
  margin-top: 30rpx;
  margin-bottom: 100rpx;
}
.login-type {
  width: 50%;
  margin: 0 auto;
  margin-bottom: 40rpx;
}
.login-form {
}
.login-item {
  background: #f6f6f6;
  border-radius: 8px;
  margin: 30rpx 60rpx;
  padding: 24rpx 40rpx;
}
.login-item .btn {
  font-size: 28rpx;
  background: #fff;
  color: $color-primary;
  padding: 15rpx 30rpx;
  border-radius: 8px;
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.02);
}
.login-btn {
  margin: 60rpx;
}

.login-other {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  margin-left: -140rpx;
  width: 280rpx;
  text-align: center;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.login-other .btn {
  background: $color-primary-light;
  color: $color-primary;

  font-size: 30rpx;
  padding: 10rpx 30rpx;
  border: 1px solid $color-primary;
  border-radius: 100rpx;
}
.index-navbar {
  height: 44px;
  position: fixed;
  left: 0;
  width: 100%;
  top: 0;
  line-height: 44px;
  background: $color-primary;
  text-align: center;
  color: #fff;
  z-index: 2222;
}
.index-head {
  height: 330rpx;
  background: $btn-bg;

  color: #000;
}
.index-head .title {
  height: 44px;
  line-height: 44px;
  padding-left: 45rpx;
  padding-right: 240rpx;
  font-weight: bold;
  font-size: 40rpx;
}
.index-head .subtitle {
  color: #333;
  font-size: 32rpx;
  padding: 0 45rpx;
}
.index-message {
  background: #fff;
  margin-top: -170rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  padding: 20rpx 0 30rpx 0;
}
.index-message .con {
  padding: 0 30rpx;
}
.index-message .icon {
  width: 80rpx;
  height: 80rpx;
  background: $color-primary;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.index-message .icon .iconfont {
  font-size: 40rpx;
}
.index-message .text {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.index-message .desc {
  font-size: 24rpx;
  color: #999;
}
.index-message .time {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}
.index-pannel {
  background: #fff;
  margin: 30rpx;
  border-radius: 12rpx;
  padding: 20rpx 0 30rpx 0;
}
.index-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.index-title .more {
  font-size: 24rpx;
  color: #999;
  margin-right: 6rpx;
}
.index-pannel .lis {
}
.index-pannel .title {
  font-weight: bold;
  padding: 30rpx;
}
.index-pannel .li {
  width: 25%;
  display: inline-block;
  text-align: center;
  margin: 20rpx 0;
}
.index-pannel .li image {
  width: 70rpx;
  height: 70rpx;
}
.index-pannel .li .text {
  font-size: 24rpx;
  margin-top: 20rpx;
}
.index-pannel .li .icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 90rpx;
  margin: 0 auto;
  border-radius: 10px;
  position: relative;
}
.index-pannel .li .icon .count {
  position: absolute;
  right: -10rpx;
  top: -10rpx;
  background: #ffc107;
  color: #fff;
  font-size: 24rpx;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
}
.index-pannel .li .iconfont {
  color: #fff;
  font-size: 48rpx;
}
.index-pannel .li .icon1 {
  background: #f85f69;
}
.index-pannel .li .icon2 {
  background: #26a8ff;
}
.index-pannel .li .icon3 {
  background: #fc7944;
}
.index-pannel .li .icon4 {
  background: #8e97fc;
}
.index-pannel .li .icon5 {
  background: #5bc49f;
}
.index-pannel .li .icon6 {
  background: #7f9fff;
}
.index-pannel .li .icon7 {
  background: #b07fff;
}

.index-count {
  margin-left: 30rpx;
  margin-right: 30rpx;
  background: #fff;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
}

.index-count .lis {
  padding: 0 15rpx 30rpx;
}
.index-count .li {
  width: 33.3%;
  display: inline-block;
  padding: 0 10rpx;
  box-sizing: border-box;
}
.index-count .wrap {
  background: linear-gradient(180deg, #dffffa, #f7fbfb);
  padding: 20rpx 24rpx;
  border-radius: 4px;
}
.index-count .wrap1 {
  background: linear-gradient(180deg, #ffdfdf, #fbf7f7);
}
.index-count .wrap2 {
  background: linear-gradient(180deg, #fbefe5, #fbf7f7);
}
.index-count .iconfont {
  color: $color-primary;
  font-size: 32rpx;
  margin-right: 4px;
}
.index-count .wrap1 .iconfont {
  color: #f56e6e;
}
.index-count .wrap2 .iconfont {
  color: #f0952d;
}
.index-count .text {
  color: #333;
  font-size: 28rpx;
}
.index-count .count {
  font-size: 48rpx;
  font-weight: bold;
  margin: 30rpx 0 0;
  color: #000;
  line-height: 1;
}
.pannel-title {
  padding: 30rpx 0;
  margin: 0 30rpx;
}
.pannel-title .more {
  font-size: 24rpx;
}

.pannel-title .status {
  font-size: 24rpx;
  color: #fff;
  background: $color-primary;
  padding: 2px 6px;
  border-radius: 4px;
}
.pannel-title .status1 {
  background: #ccc;
}
.pannel-title .status2 {
  background: $color-primary;
}
.pannel-title .status3 {
  background: #e94b46;
}

.title-content {
  font-weight: bold;
  line-height: 1;
}
.title-content {
  position: relative;
  padding-left: 20rpx;
}
.title-content::before {
  content: "";
  position: absolute;
  background: $color-primary;
  height: 100%;
  width: 3px;
  border-radius: 100px;
  left: 0;
}

.index-notice {
  background: #fff;
  margin: 30rpx;
  border-radius: 12rpx;
}
.index-notice .li {
  padding: 30rpx 0;
}
.index-notice .list {
  margin: 0 30rpx;
}
.index-notice .text {
  font-size: 30rpx;
  color: #000;
  margin-bottom: 10rpx;
}
.index-notice .desc {
  font-size: 24rpx;
  color: #666;
}
.index-notice .time {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}
.index-notice .time .t1 {
  font-size: 28rpx;
  color: $color-primary;
}
.index-notice .status {
  font-size: 24rpx;
  color: #fff;
  background: $color-primary;
  padding: 2px 6px;
  border-radius: 4px;
}
.index-notice .status1 {
  background: #f56e6e;
}
.index-notice .status2 {
  background: #f0952d;
}
.index-notice .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: $color-primary;
  margin-right: 20rpx;
}
.index-section {
  width: 300rpx;
  margin: 30rpx auto;
}
.index-title-count {
  text-align: center;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}
.index-title-count .count {
  font-size: 28rpx;
  color: #fff;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  border-radius: 50%;
  background: $color-primary;
  margin-left: 10rpx;
}
.u-info {
  padding: 30rpx;
  background: #fff;
}
.u-info image {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.u-info .name {
  color: #000;
  font-size: 36rpx;
  margin-bottom: 10rpx;
}
.u-info .desc {
  font-size: 30rpx;
}

.u-info .tag {
  font-size: 24rpx;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 4px;
}
.u-info .tag.tag-success {
  background: $color-primary;
}
.u-info .tag.tag-error {
  background: #ddd;
}
.user-cells {
  background: #fff;
  margin-top: 20rpx;
}
.user-cells .i {
  padding: 40rpx 0;
  margin: 0 30rpx;
  font-size: 32rpx;
}
.user-cells .icon image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.form-pannel {
  background: #fff;
  margin: 30rpx;
  border-radius: 8px;
}

.form-pannel .form {
  margin: 0 30rpx;
}

.fix-tab {
  background: #fff;
  position: fixed;
  left: 0;
  width: 100%;
  z-index: 10;
}
.fix-tab .search-box {
  background: #fff;
  padding: 10px 30rpx;
}
.fix-tab-blank {
  height: 44px;
}

.repaire-list .li {
  background: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 12rpx;
}
.repaire-list .li-top {
  margin-bottom: 20rpx;
}
.repaire-list .li-top .left {
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}
.repaire-list .li-top .status {
  font-size: 24rpx;
  color: #fff;
  padding: 2px 0;
  border-radius: 4px;
  width: 120rpx;
  text-align: center;
}
.repaire-list .li-top .status1 {
  background: #ccc;
}
.repaire-list .li-top .status2 {
  background: $color-primary;
}
.repaire-list .li-top .status3 {
  background: #e94b46;
}
.repaire-list .li-bottom {
  font-size: 28rpx;
  color: #999;
}
.repaire-list .li-bottom .flex {
  margin-top: 12rpx;
}
.repaire-list .li-bottom .flex-hd {
  width: 130rpx;
}
.repaire-list .li-bottom .flex-bd {
  color: #333;
}
.repaire-list .cate {
  margin: 20rpx 0;
}
.repaire-list .cate .span {
  display: inline-block;
  background: $color-primary-light;
  color: $color-primary;
  font-size: 24rpx;
  border-radius: 4px;
  padding: 0 10rpx;
}
.repaire-list .now {
  margin-top: 15rpx;
}
.repaire-list .now .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: $color-primary;
}
.repaire-list .now .flex-bd {
  font-size: 24rpx;
  margin-left: 10rpx;
}
.repaire-list .li .icon {
  width: 80rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.repaire-list .li .icon .iconfont {
  color: #fff;
  font-size: 48rpx;
}
.repaire-list .li .icon1 {
  background: #08cc91;
}
.repaire-list .li .icon2 {
  background: #19c2d1;
}
.repaire-list .li .icon3 {
  background: #fc7944;
}
.repaire-list .li .icon4 {
  background: #8e97fc;
}

.people-list {
}
.people-list .li {
  padding: 30rpx;
}
.people-list image {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.people-list .name {
  margin-bottom: 10rpx;
}
.people-list .no {
  font-size: 24rpx;
  color: #999;
}

.top-search {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 20rpx;
}

.top-search .flex-hd {
  margin-left: 20rpx;
}
.top-search .flex-hd i {
  font-size: 42rpx;
}
.fixed-tab {
  width: 100%;
  top: 0;
  background: #fff;
  position: fixed;
  z-index: 2;
  height: 58px;
}

.fixed-tab .u-tabs__wrapper__nav__item__text {
  font-size: 28rpx !important;
}

.his-line {
}
.his-line .li {
  padding-bottom: 40rpx;
  position: relative;
}
.his-line .li .flex-hd {
  margin-right: 20rpx;
}
.his-line .li .dot {
  width: 11px;
  height: 11px;
  border-radius: 50%;
  background: $color-primary;
  margin-top: 6rpx;
}
.his-line .li .line {
  width: 1px;
  height: 100%;
  background: #ddd;
  margin-left: 5px;
  position: absolute;
}
.his-line .li:last-child .line {
  display: none;
}
.his-line .li:last-child {
  padding-bottom: 0;
}
.his-line .li .time {
  font-size: 30rpx;
  margin-bottom: 20rpx;
}
.his-line .li .desc {
  color: #999;
  font-size: 26rpx;
}
.his-line .li .desc .p {
  margin-top: 10rpx;
}
.his-line .li .desc .inline {
  color: #333;
}
.his-line .li .time {
  font-size: 28rpx;
  margin-right: 10rpx;
}
.his-line .li .bg {
  background: #f6f6f6;
  padding: 20rpx;
  border-radius: 6px;
}
.his-line .li .con {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
  line-height: 1.6;
}

.fixbtn .btns {
  height: 90rpx;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background: #fff;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom)); /* 设置底部安全距离 */
  z-index: 69;
}
.fixbtn .blank {
  height: 90rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom)); /* 设置底部安全距离 */
}
.fixbtn .btns .btn {
  width: 100%;
  font-size: 28rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 4px;
  background: $color-primary;
  color: #fff;
  margin: 0 20rpx;
}
.fixbtn .btns .btn + .btn {
  margin-left: 0;
}
.fixbtn .btns .btn.bor {
  border: 1px solid $color-primary;
  color: $color-primary;
  background: transparent;
}

.message-list {
}
.message-list .li {
  background: #fff;
  padding: 30rpx;
  position: relative;
  margin: 30rpx;
  border-radius: 8px;
}
.message-list .icon {
  width: 80rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.message-list .icon .iconfont {
  color: #fff;
  font-size: 48rpx;
}

.message-list .icon1 {
  background-image: linear-gradient(135deg, #66a2f8, #4f8ff3);
}

.message-list .icon2 {
  background-image: linear-gradient(135deg, #5dc7d4, #2ebddb);
}

.message-list .icon3 {
  background-image: linear-gradient(135deg, #7fcb8b, #59c067);
}

.message-list .icon4 {
  background-image: linear-gradient(135deg, #f6b152, #f4a73b);
}

.message-list .icon5 {
  background-image: linear-gradient(135deg, #ee8975, #ee7a61);
}

.message-list .icon6 {
  background-image: linear-gradient(135deg, #1bca99, #1bca99);
}
.message-list .title {
  font-size: 30rpx;
  margin-bottom: 10rpx;
}
.message-list .con {
  font-size: 26rpx;
  color: #999;
}
.message-list .time {
  font-size: 24rpx;
  color: #999;
}
.message-list .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.message-list .dot1 {
  background: #ff5500;
}
.message-list .dot2 {
  background: $color-primary;
}

.message-list .dot1 {
  background: #ff5500;
}

.feedback {
  padding: 30rpx;

  .type-list {
  }
  .type-list .i {
    display: inline-block;
    padding: 16rpx 30rpx;
    border-radius: 100px;
    background: #f6f6f6;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    width: 20%;
    text-align: center;
    font-size: 28rpx;
  }
  .type-list .i.active {
    background: $color-primary;
    color: #fff;
  }
  .text {
    background: #f6f6f6;
    padding: 20rpx;
  }
  .tel {
    background: #f6f6f6;
    margin: 20rpx 0;
    padding: 20rpx 30rpx;
  }
  .tel .tit {
    font-weight: bold;
    color: #333;
  }
  .tel .bot {
    font-weight: bold;
    color: #333;
  }
  .tel .cons {
    padding-bottom: 15rpx;
  }
  .tel .cons .i {
    font-size: 24rpx;
  }
}
.feedback .stitle {
  line-height: 1;
  padding-left: 15rpx;
  font-size: 32rpx;
  border-left: 4px solid $color-primary;
  margin: 40rpx 0;
  font-weight: bold;
}
.userInfo .u-info {
  margin-top: 0;
  .avatar {
    margin: 0 auto;
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    overflow: hidden;
    image {
      width: 160rpx;
      height: 160rpx;
    }
    .edit {
      background: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: 0;
      left: 0;
      width: 160rpx;
      height: 160rpx;
      color: #fff;
      line-height: 160rpx;
      text-align: center;
      font-size: 28rpx;
    }
  }
  .item {
    padding: 30rpx 0;
  }
  .flex-hd {
    width: 160rpx;
  }
}

.content-box {
  box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  margin: 20rpx;
  background: #fff;
  border-radius: 8px;
}
.content-box .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx;
}
.content-box .title .flex-hd {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}
.content-box .title .search {
  width: 400rpx;
}

.content-box .s-select {
  text-align: center;
  margin-bottom: 30rpx;
}
.content-box .s-select .i {
  display: inline-block;
  border: 1px solid #ddd;
  padding: 10rpx;
  margin: 0 5rpx;
  border-radius: 4px;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.content-box .s-select .i.active {
  border: 1px solid #008d57;
  background: #008d57;
  color: #fff;
}

.content-box .s-title {
  font-size: 28rpx;
  color: #444;
  margin: 0 30rpx 30rpx;
  font-weight: bold;
}

.fixed-add {
  bottom: 100rpx;
  right: 40rpx;
  width: 90rpx;
  height: 90rpx;
  line-height: 90rpx;
  position: fixed;
  z-index: 10;
  background: $btn-bg;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.1);
}

.fixed-add .iconfont {
  font-size: 48rpx;
}

.project-list {
  margin: 30rpx;
}

.project-list .project-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
}

.project-list .flex-hd image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.project-list .flex-bd {
  flex: 1;
  margin: 0 24rpx;
}

.project-list .project-name .name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.project-list .project-name .des {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

.project-list .project-name .status {
  display: flex;
  margin-top: 12rpx;
}

.project-list .item {
  margin-right: 40rpx;
  font-size: 24rpx;
  color: #666;
  flex: 1;
  color: $color-primary;
}

.project-list .item .num {
  color: #f56c6c;
  text-align: center;
  margin-left: 4rpx;
}

.project-list .flex-ft {
  display: flex;
  align-items: center;
  color: #999;
}
.project-list .selected-icon {
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px 10rpx;
  border-bottom-left-radius: 12rpx;
  background: $color-primary;
}
.bind-weixin {
  margin-top: 100rpx;
}
.bind-weixin .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.bind-weixin .tis {
  margin: 30rpx 0 60rpx 0;
}
.bind-weixin .tips {
  font-size: 24rpx;
  color: #999;
}
.form-cells {
}
.form-item {
  padding: 16rpx 0;
  font-size: 28rpx;
  align-items: flex-start;
  line-height: 1.6;
}
.form-cells .label {
  width: 160rpx;
  color: #999;
}
.form-cells .flex-bd {
  flex: 1;
}
.empty {
  text-align: center;
  padding: 30rpx;
}
.empty image {
  width: 200rpx;
  height: 200rpx;
}
.empty .text {
  font-size: 30rpx;
  color: #999;
  margin-top: 20rpx;
}
.project-info {
  padding: 30rpx;
  margin: 30rpx;
  background: #fff;
  border-radius: 12rpx;
}
.project-info .flex-hd image {
  width: 120rpx;
  height: 120rpx;
}

.project-info .flex-bd {
  flex: 1;
  margin: 0 24rpx;
}

.project-info .project-name .name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.project-info .project-name .des {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}
.people-list .li {
  padding: 30rpx;
  font-size: 30rpx;
}
.people-list .li .image {
  width: 90rpx;
  height: 90rpx;
  margin: 0 20rpx;
}

.people-list .li .tag {
  color: #999;
}
.dish-list .li {
  padding: 30rpx;
}

.dish-list .li .image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
}

.dish-list .li .image image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.dish-list .li .flex-bd .top {
  margin-bottom: 10rpx;
}

.dish-list .li .flex-bd .name {
  font-size: 28rpx;
  color: #333;
}

.dish-list .li .flex-bd .price {
  color: #f56c6c;
  font-size: 28rpx;
}

.dish-list .li .flex-bd .desc {
  font-size: 24rpx;
  color: #999;
}
.dish-list .li .flex-hd {
  margin-right: 20rpx;
}
.popup-content {
  padding: 30rpx;
}
.popup-content .form-pannel {
  margin: 0;
  padding: 0 0 30rpx 0;
}
.service-list .li {
  padding: 30rpx;
}
.service-list .li .flex-hd {
  margin-right: 20rpx;
}
.rz-content {
  text-align: center;
  width: 500rpx;
}
.rz-content .rz-img {
  width: 190rpx;
  height: 190rpx;
}
.rz-content .rz-title {
  margin: 40rpx 0;
}

.rz-btns .inline {
  width: 200rpx;
  margin: 0 20rpx;
}
.meetPeopleList {
}
.meetPeopleList .li {
  padding: 10rpx 0;
}
.meetPeopleList .li .name {
}
.meetPeopleList .li .tag {
  font-size: 24rpx;
  color: #fff;
  padding: 2px 10rpx;
  border-radius: 2px;
  margin-left: 10rpx;
  background: #f0952d;
}
.meetPeopleList .li .status {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}
.radio-list {
}
.radio-list .radio-items {
  margin-bottom: 20rpx;
}
.radio-list .radio-items:last-child {
  margin-bottom: 0;
}
.radio-list .radio-items .title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.radio-list .radio-items .radio {
  display: flex;
  align-items: center;
}
.radio-list .radio-items .radio .radio-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #999;
}
.radio-list .radio-items .radio .radio-item .inline {
  margin-right: 10rpx;
}
.contact {
  background: #fff;
}
.contact .li {
  margin: 0 30rpx;
  padding: 30rpx 0;
  position: relative;
}
.contact .avatar {
  height: 80rpx;
  width: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}
.contact .li .name {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}
.contact .li .role {
  font-size: 28rpx;
  color: #999;
  margin-top: 10rpx;
}
.contact .li .role-item {
  color: #666;
  background: #f6f6f6;
  margin-right: 10rpx;
  padding: 2px 10rpx;
  border-radius: 2px;
  display: inline-block;
  font-size: 24rpx;
}
.contact .li .tel {
  width: 60rpx;
  height: 60rpx;
  background: $color-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #fff;
  font-size: 32rpx;
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.1);
}
.contact .li .tag {
  font-size: 24rpx;
  margin-right: 10rpx;
  color: #fff;
  background: #f0952d;
  border-radius: 2px;
  display: inline-block;
  width: 60rpx;
  text-align: center;
}
.pop-select {
  width: 100%;
  background: #fff;
}
.pop-select .title {
  height: 44px;
  line-height: 44px;
  padding: 0 30rpx;
  font-weight: bold;
}

.status-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.status-item {
  padding: 15rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;

  &.active {
    background-color: $color-primary;
    color: #fff;
  }
}
.time-search {
  margin: 30rpx;
}
.time-search .title {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}
.time-search .btn {
  margin-left: 20rpx;
}
.float-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: $color-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(41, 121, 255, 0.3);
}
.image-list {
}
.image-list image {
  width: 60rpx;
  height: 60rpx;
  margin-right: 10rpx;
  margin-top: 10rpx;
}
.asset-list .li {
  background: #fff;
  padding: 20rpx;
  position: relative;
  margin: 30rpx;
  border-radius: 8px;
}
.asset-list .li image {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}

.asset-list .li .flex-bd .name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.asset-list .li .flex-bd .infos {
  font-size: 24rpx;
  color: #999;
}

.u-textarea {
  padding: 0 !important;
}

.bot-blank {
  padding-bottom: var(--safe-area-inset-bottom);
}

.deal-list {
  margin-top: 30rpx;
}
.deal-list .list {
  margin-top: 30rpx;
}
.deal-list .item {
  padding-bottom: 30rpx;
  position: relative;
}

.deal-list .item .icon {
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background: $color-primary;
  color: #fff;
  line-height: 17px;
  position: relative;
  z-index: 2;

  display: flex;
  align-items: center;
  justify-content: center;
}
.deal-list .item .con {
  background: #fff;
  border-radius: 6px;
  margin-left: 30rpx;
  position: relative;
  font-size: 28rpx;
}
.deal-list .item .line {
  width: 1px;
  background: #ddd;
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  transform: translateX(-50%);
}
.deal-list .item:first-child .line {
  top: 20rpx;
}
.deal-list .item:last-child .line {
  display: none;
}
.deal-list .item .content {
  margin-top: 10rpx;
}
.deal-list .item .imgs {
  margin-top: 10rpx;
}
.deal-list .item .time {
  font-size: 28rpx;
  color: #999;
  line-height: 1;
}
.index-notice {
  padding: 20rpx;
  border-radius: 6px;
}
.index-notice .flex-hd {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}
.index-notice .flex-hd .name {
  color: #ff790c;
}
.index-notice .flex-bd {
  font-size: 28rpx;
  color: #333;
}
