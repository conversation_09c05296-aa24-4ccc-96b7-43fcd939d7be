<template>
  <view>
    <view class="index-navbar" :style="'padding-top:' + barhei + 'px;opacity:' + opacity">
      <view class="tit">欢迎使用物业管理平台</view>
    </view>
    <view class="index-head" :style="'padding-top:' + barhei + 'px;'">
      <view class="title line-1">欢迎使用物业管理平台</view>
      <view class="subtitle">您好，{{ UserInfo.UserName }}</view>
    </view>
    <view class="index-message">
      <view class="index-title flex bor-b" @click="$c.naviTo('../noticeList/noticeList')">
        <view class="flex-bd">通知公告</view>
        <view class="more">查看全部通知</view>
      </view>
      <view class="con flex">
        <view class="icon">
          <i class="iconfont icon-labamoren"></i>
        </view>
        <view class="flex-bd" v-if="notice.Title" @click="$c.naviTo('../noticeDetail/noticeDetail?Code=' + notice.Code)">
          <view class="text flex">
            <view class="flex-bd">{{ notice.Title }}</view>
            <view class="time">{{ notice.CreateDateTimeCaption }}</view>
          </view>
          <view class="desc">{{ notice.NoticeContent }}</view>
        </view>
        <view class="flex-bd" v-else>
          <view class="text flex">
            <view class="flex-bd">暂无未读通知</view>
          </view>
        </view>
      </view>
    </view>
    <view class="index-title-count">
      <view class="inline">我的项目</view>
      <view class="inline count">{{ projectNum }}</view>
    </view>
    <view class="project-list">
      <view class="project-item flex" v-if="item.ProjectInfo.ProjectStatus == 1" v-for="(item, index) in list" @click="$c.naviTo('../projectIndex/projectIndex?Code=' + item.ProjectInfo.Code)">
        <view class="flex-hd">
          <image :src="$c.getFullImage(item.ProjectInfo.MainImage)" mode="aspectFill"></image>
        </view>
        <view class="flex-bd">
          <view class="project-name">
            <view class="name">{{ item.ProjectInfo.ProjectName }}</view>
            <view class="des">
              <view class="line-1">地址：{{ item.ProjectInfo.Address ? item.ProjectInfo.Address : "暂无地址" }}</view>
              <view>项目负责人：{{ item.ProjectInfo.UserName ? item.ProjectInfo.UserName : "暂无负责人" }}</view>
            </view>
            <!-- <view class="status flex">
              <view class="item flex">
                <view>待处理工单</view>
                <view class="num">{{ item.GdCount }}</view>
              </view>
              <view class="item flex">
                <view>待处理预约</view>
                <view class="num">{{ item.YyCount }}</view>
              </view>
            </view> -->
          </view>
        </view>
        <view class="flex-ft">
          <u-icon name="arrow-right"></u-icon>
        </view>
        <view class="selected-icon" v-if="item.selected">
          <u-icon name="checkbox-mark" color="#fff" size="18"></u-icon>
        </view>
      </view>
      <view v-if="list.length == 0">
        <view class="empty">
          <image src="@/static/images/nores.png"></image>
          <view class="text">暂无内容</view>
        </view>
      </view>
    </view>
    <!-- 提示认证弹出框 -->
    <u-modal :show="rzShow" :showConfirmButton="false">
      <view class="rz-content">
        <image src="/static/images/icon-rz.png" mode="widthFix" class="rz-img"></image>
        <view class="rz-title">请先进行认证</view>
        <view class="rz-btns">
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="info" :customStyle="'height:80rpx;'" @click="logout">退出登录</u-button>
          </view>
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle() + 'height:80rpx;'" @click="$c.naviTo('../auth/auth')">确定</u-button>
          </view>
        </view>
      </view>
    </u-modal>
    <!-- 认证中弹出框 -->
    <u-modal :show="rzIngShow" :showConfirmButton="false">
      <view class="rz-content">
        <image src="/static/images/icon-rz.png" mode="widthFix" class="rz-img"></image>
        <view class="rz-title">认证中...</view>
        <view class="rz-btns">
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="info" :customStyle="'height:80rpx;'" @click="logout">退出登录</u-button>
          </view>
          <view class="inline">
            <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle() + 'height:80rpx;'" @click="$c.naviTo('../auth/auth')">确定</u-button>
          </view>
        </view>
      </view>
    </u-modal>
    <!-- 添加u-tabbar -->
    <u-tabbar :value="current" :fixed="true" @change="tabbarChange" :activeColor="$c.color()" :placeholder="true" :safeAreaInsetBottom="true">
      <u-tabbar-item text="首页" icon="home"></u-tabbar-item>
      <u-tabbar-item text="通讯录" icon="phone"></u-tabbar-item>
      <u-tabbar-item text="我的" icon="account"></u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
export default {
  data() {
    return {
      current: 0,
      barhei: 0,
      UserInfo: uni.getStorageSync("UserInfo"),
      list: [],
      notice: {},
      rzShow: false,
      rzIngShow: false,
      projectNum: 0,
      opacity: 0,
    };
  },
  onLoad() {
    uni.hideTabBar();
    var sys = uni.getSystemInfoSync();
    this.barhei = sys.statusBarHeight;

    if (!uni.getStorageSync("UserCode")) {
      return uni.navigateTo({ url: "../login/login" }); //跳转到登录页
    }

    this.$apis
      .getNoticeList({
        PageIndex: 1,
        PageSize: 1,
        UserCode: uni.getStorageSync("UserCode"), //用户编码
        IsSee: 0, //是否查看 0-否 1- 是
        ProjectCode: "", //项目编码
      })
      .then((res) => {
        if (res.data && res.data[0]) {
          res.data[0].NoticeContent = this.$c.getText(res.data[0].NoticeContent);
          this.notice = res.data[0];
        }
      });
  },
  onShow() {
    this.$apis
      .getUserInfo(
        {
          CellPhone: this.UserInfo.CellPhone,
        },
        { loading: false }
      )
      .then((res) => {
        this.UserInfo = res.data;
        uni.setStorageSync("UserInfo", res.data);
        //判断有没有认证;
        if (uni.getStorageSync("UserInfo").IsCheck == 0) {
          this.rzShow = true;
        }
        if (uni.getStorageSync("UserInfo").IsCheck == 1) {
          this.rzIngShow = true;
        }
        if (uni.getStorageSync("UserInfo").IsCheck == 2) {
          this.$apis
            .getMyProjectList(
              {
                UserCode: this.UserInfo.Code,
              },
              { loading: false }
            )
            .then((res) => {
              var data = [];
              for (var i = 0; i < res.data.length; i++) {
                if (res.data[i].ProjectInfo.ProjectStatus == 1) {
                  data.push(res.data[i]);
                }
              }
              this.list = data;
              this.projectNum = data.length;
            });
        }
      });
  },
  methods: {
    tabbarChange(index) {
      const tabList = ["../index/index", "../contacts/contacts", "../user/user"];
      uni.switchTab({
        url: tabList[index],
      });
    },
    logout() {
      uni.setStorageSync("UserInfo", "");
      uni.setStorageSync("UserCode", "");
      uni.setStorageSync("UserType", "");
      uni.navigateTo({
        url: "../login/login",
      });
    },
  },
  onPageScroll(e) {
    if (e.scrollTop > 120) {
      var top = (e.scrollTop - 120) / 120;
      this.opacity = top > 1 ? 1 : top;
    } else {
      this.opacity = 0;
    }
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
