<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="报事报修" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <u-tabs :scrollable="false" @click="tabClick" :list="tabs" :lineColor="$c.color()" :activeStyle="'color:' + $c.color()"></u-tabs>
    </view>
    <view class="scroll" style="padding-top: 44px">
      <view class="repaire-list">
        <view class="li" @click="detail(item)" v-for="(item, index) in list">
          <view class="li-top flex">
            <view class="left flex-bd">单号:{{ item.RepairOrderNo }}</view>
          </view>
          <view class="li-bottom">
            <view class="flex">
              <view class="flex-hd">上报人</view>
              <view class="flex-bd">{{ item.ApplyUserName }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">上报时间</view>
              <view class="flex-bd">{{ item.ApplyTimeCaption }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">上报地点</view>
              <view class="flex-bd line-1">{{ item.Address }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">上报内容</view>
              <view class="flex-bd line-1">{{ item.RepairMatter }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      type: "",
      ProjectCode: "",
      tabs: [],
      list: [],
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
      RepairStatusSearch: "",
    };
  },
  onLoad(options) {
    this.type = options.type;
    this.ProjectCode = options.ProjectCode;
    if (this.type == "apply") {
      if (uni.getStorageSync("UserInfo").CurRolesCaption.split(",").includes("维修服务负责人")) {
        this.tabs = [
          { name: "待指派", value: "待指派" },
          { name: "待接单", value: "待接单" },
          { name: "进行中", value: "进行中" },
          { name: "已完成", value: "已完成" },
        ];
        this.RepairStatusSearch = "待指派";
      } else {
        this.tabs = [
          { name: "待接单", value: "待接单" },
          { name: "进行中", value: "进行中" },
          { name: "已完成", value: "已完成" },
        ];
        this.RepairStatusSearch = "待接单";
      }
    } else {
      this.tabs = [
        { name: "待处理", value: "待维修" },
        { name: "待评价", value: "待评价" },
        { name: "已完成", value: "已完成" },
      ];
      this.RepairStatusSearch = "待维修";
    }
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    tabClick(e) {
      console.log(e);
      if (this.RepairStatusSearch != e.name) {
        this.RepairStatusSearch = e.name;
        this.page = 1;
        this.list = [];
        this.getList(1).then((res) => {
          this.list = res;
        });
      }
    },
    detail(item) {
      uni.navigateTo({ url: "../repaireDetail/repaireDetail?Code=" + item.Code + "&type=" + this.type });
    },
    getList(page) {
      return new Promise((resolve) => {
        if (this.type == "apply") {
          this.$apis
            .getRepairApplyList(
              {
                ProjectCode: this.ProjectCode,
                GlobalUserCode: uni.getStorageSync("UserCode"),
                RepairStatusSearch: this.RepairStatusSearch, //待指派、待接单、已接单/进行中、已完成
                RepairOrderNo: "",
                PageIndex: page,
                PageSize: 10,
              },
              { loading: false }
            )
            .then((res) => {
              if (res.data.length < 10) {
                this.loadmore.status = "nomore";
              }
              resolve(res.data);
            });
        } else {
          this.$apis
            .getMyRepairList(
              {
                ProjectCode: this.ProjectCode,
                RepairStatusSearch: this.RepairStatusSearch, //待维修，待评价，已完成
                ApplyUserCode: uni.getStorageSync("UserCode"),
                RepairOrderNo: "",
                PageIndex: page,
                PageSize: 10,
              },
              { loading: false }
            )
            .then((res) => {
              if (res.data.length < 10) {
                this.loadmore.status = "nomore";
              }
              resolve(res.data);
            });
        }
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
