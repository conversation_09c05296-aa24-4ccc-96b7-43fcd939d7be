<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="包厢预定" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <u-tabs :scrollable="false" @click="tabClick" :list="tabs" :lineColor="$c.color()" :activeStyle="'color:' + $c.color()"></u-tabs>
    </view>
    <view class="scroll" style="padding-top: 44px">
      <view class="repaire-list">
        <view class="li" @click="detail(item)" v-for="(item, index) in list">
          <view class="li-top flex">
            <view class="left flex-bd">申请单号:{{ item.BoxOrderNo }}</view>
            <view class="status status1" v-if="item.ApplyStatus == 0">{{ item.ApplyStatusCaption }}</view>
            <view class="status status2" v-else>{{ item.ApplyStatusCaption }}</view>
          </view>
          <view class="li-bottom">
            <view class="flex">
              <view class="flex-hd">预约时间</view>
              <view class="flex-bd">{{ item.ApplyTimeCaption }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">申请人</view>
              <view class="flex-bd">{{ item.ApplyUserName }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabs: [
        { name: "待处理", value: "待处理" },
        { name: "待用餐", value: "待用餐" },
        { name: "已完成", value: "已完成" },
      ],
      list: [],
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
      ApplyStatusSearch: "待处理",
      ProjectCode: "",
      type: "",
    };
  },
  onLoad(options) {
    this.type = options.type;
    this.ProjectCode = options.ProjectCode;
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    tabClick(e) {
      if (this.ApplyStatusSearch != e.name) {
        this.ApplyStatusSearch = e.name;
        this.page = 1;
        this.list = [];
        this.getList(1).then((res) => {
          this.list = res;
        });
      }
    },
    detail(item) {
      uni.navigateTo({
        url: "../boxDetail/boxDetail?Code=" + item.Code + "&type=" + this.type,
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getBoxApplyList(
            {
              ProjectCode: this.ProjectCode,
              GlobalUserCode: uni.getStorageSync("UserCode"),
              ApplyStatusSearch: this.ApplyStatusSearch,
              BoxOrderNo: "",
              PageIndex: page,
              PageSize: 10,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") return;
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
