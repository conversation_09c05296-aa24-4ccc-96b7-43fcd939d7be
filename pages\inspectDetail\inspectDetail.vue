<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" :title="moduleName" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">{{ label.Type }}详情</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">{{ label.EquipmentName }}</view>
          <view class="flex-bd">{{ form.EquipmentName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">{{ label.EquipmentNumber }}</view>
          <view class="flex-bd">{{ form.EquipmentNumber }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">{{ label.InspectionTime }}</view>
          <view class="flex-bd">{{ form.CycleType == "按周期" ? form.InspectionTime + form.InspectionUnit : form.InspectionWeek + form.TimeRanges }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">{{ label.LastInspectionTime }}</view>
          <view class="flex-bd">{{ $c.formatDate(form.LastInspectionTime) }}</view>
        </view>
      </view>
    </view>

    <view class="time-search flex">
      <view class="title">历史记录</view>
      <view class="input flex-bd" @click="showCalendar = true">
        <uni-datetime-picker :border="false" :start="minDate" :end="maxDate" v-model="datetimerange" type="daterange" @change="calendarConfirm" @clear="calendarConfirm" rangeSeparator="至" />
      </view>
      <!-- <view class="btn">
        <u-button type="info" customStyle="height:35px;border:0;'" @click="search">搜索</u-button>
      </view> -->
    </view>

    <view class="repaire-list">
      <view class="li" @click="$c.naviTo('../inspectCheckDetail/inspectCheckDetail?Code=' + item.Code + '&moduleName=' + moduleName)" v-for="(item, index) in list">
        <view class="li-top flex">
          <view class="left flex-bd">{{ label.InspectorUserName }}:{{ item.InspectorUserName }}</view>
        </view>
        <view class="li-bottom">
          <view class="flex">
            <view class="flex-hd">{{ label.InspectionTime }}</view>
            <view class="flex-bd">{{ $c.formatTime(item.InspectionTime) }}</view>
          </view>
          <view class="flex">
            <view class="flex-hd">{{ label.InspectionContent }}</view>
            <view class="flex-bd line-1">{{ item.InspectionRemarkStr }}</view>
          </view>
          <view class="flex flext">
            <view class="flex-hd">备注</view>
            <view class="flex-bd">{{ item.InspectionContent }}</view>
          </view>
          <view class="flex flext" v-if="item.Images.length > 0">
            <view class="flex-hd">{{ label.Images }}</view>
            <view class="flex-bd">
              <view class="image-list">
                <image v-for="(i, index) in item.Images" :src="i" mode="aspectFill" @click.stop="$c.viewImage(item.Images, index)"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <view class="float-btn" @click="goAdd">
      <u-icon name="plus" color="#fff" size="24"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      Code: "",
      form: {
        MeterType: "",
        MeterNumber: "",
        CycleType: "",
        InspectionTime: "",
        InspectionUnit: "",
        InspectionWeek: "",
        TimeRanges: "",
      },
      list: [],
      showCalendar: false,
      startDate: "",
      endDate: "",
      datetimerange: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      minDate: "",
      maxDate: "",
      moduleName: "",
      label: {},
    };
  },
  onLoad(options) {
    this.moduleName = options.moduleName;
    this.label = this.$c.switchInspectionLabel(this.moduleName);
    this.Code = options.Code;
    this.getDetail();
    this.getList(1).then((res) => {
      this.list = res;
    });
    this.minDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1)).getTime();
    this.maxDate = new Date().getTime();
  },
  onShow() {
    uni.$on("refreshInspectList", (data) => {
      this.page = 1;
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
      uni.$off("refreshInspectList");
    });
  },
  methods: {
    calendarConfirm(e) {
      console.log(e);
      this.startDate = e[0];
      this.endDate = e[1];
      this.page = 1;
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    getDetail() {
      this.$apis.getInspectDetail({ Code: this.Code }).then((res) => {
        this.form = res.data;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        let params = {
          EquipmentCode: this.Code,
          PageIndex: page,
          PageSize: 10,
        };
        if (this.startDate && this.endDate) {
          params.InspectionTime = this.startDate + "~" + this.endDate;
        }
        this.$apis.getInspectRecordList(params, { loading: false }).then((res) => {
          if (res.data.length < 10) {
            this.loadmore.status = "nomore";
          }
          if (res.data.length > 0) {
            for (let i in res.data) {
              if (res.data[i].Images) {
                var arr = res.data[i].Images.split(",");
                for (let j in arr) {
                  arr[j] = this.$c.getFullImage(arr[j]);
                }
                res.data[i].Images = arr;
              }
            }
          }
          resolve(res.data);
        });
      });
    },
    goAdd() {
      uni.navigateTo({
        url: `../inspectCheckAdd/inspectCheckAdd?Code=${this.Code}&moduleName=${this.moduleName}&ProjectCode=${this.form.ProjectCode}`,
      });
    },
    goDetail(item) {
      uni.navigateTo({
        url: `../inspectCheckDetail/inspectCheckDetail?Code=${item.Code}&moduleName=${this.moduleName}`,
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style lang="scss">
page {
  background: #f6f6f6;
}
.form-cells .label {
  width: 200rpx;
}
</style>
