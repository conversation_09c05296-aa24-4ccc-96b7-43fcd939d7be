<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="绑定微信" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="bind-weixin tc">
      <view class="icon">
        <view class="inline">
          <u-icon name="weixin-fill" size="100" color="#159e28"></u-icon>
        </view>
      </view>
      <view class="tis">
        <view class="title" v-if="!XcxOpenid">未绑定</view>
        <view class="title" v-else>已绑定</view>
        <view class="tips">绑定微信后可以接收公众号消息通知</view>
      </view>
    </view>
    <view class="mr30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">{{ XcxOpenid ? "解绑" : "绑定" }}</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      openid: "123",
      UserCode: uni.getStorageSync("UserCode"),
      XcxOpenid: uni.getStorageSync("UserInfo").XcxOpenid,
    };
  },
  onLoad() {},

  methods: {
    submit() {
      uni.login({
        provider: "weixin",
        success: (res) => {
          console.log(res.code);
          var jscode = res.code;
          this.$apis[this.XcxOpenid ? "unbindOpenid" : "getUnionid"]({
            JsCode: jscode,
            GlobalUserCode: this.UserCode,
            Code: this.UserCode,
          }).then((res) => {
            console.log(res);
            if (res.code == 100) {
              uni.showToast({ mask: true, title: this.XcxOpenid ? "解绑成功" : "绑定成功", icon: "success" });
              // 更新缓存中的XcxOpenid
              const userInfo = uni.getStorageSync("UserInfo");
              userInfo.XcxOpenid = this.XcxOpenid ? null : this.openid;
              this.XcxOpenid = userInfo.XcxOpenid;
              uni.setStorageSync("UserInfo", userInfo);
            } else {
              this.$u.toast(res.msg);
            }
          });
        },
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
