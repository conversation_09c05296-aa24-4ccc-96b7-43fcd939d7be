<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="工作日报" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">登记信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 填报日期 -->
          <u-form-item label="填报日期" borderBottom>
            <u-input v-model="form.DailyTime" disabledColor="#fff" disabled border="none" placeholder="请输入抄表日期" />
          </u-form-item>
          <!-- 内容 -->
          <u-form-item label="内容" borderBottom>
            <u-textarea v-model="form.DailyContent" border="none" placeholder="请输入内容" />
          </u-form-item>
          <!-- 图片上传 -->
          <u-form-item label="图片">
            <my-upload v-model="fileList" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showDatePicker: false,
      form: {
        DailyUserCode: uni.getStorageSync("UserCode"),
        DailyContent: "",
        Images: "",
        ProjectCode: "",
        DailyTime: "",
      },
      fileList: [],
    };
  },
  onLoad(options) {
    this.form.ProjectCode = options.ProjectCode;
    this.form.DailyTime = this.$u.timeFormat(new Date(), "yyyy-mm-dd");
  },
  methods: {
    handleUploadChange(data) {
      this.form.Images = data.urls;
    },
    submit() {
      if (!this.form.DailyContent) {
        this.$u.toast("请输入日报内容");
        return false;
      }
      this.$apis
        .addDaily(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);

            uni.$emit("refreshDailyReportList", true);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
