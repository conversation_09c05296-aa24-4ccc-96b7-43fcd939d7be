<template>
  <view class="userInfo">
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="用户认证" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="u-info">
      <view class="form">
        <view class="item bor-b flex">
          <view class="flex-hd">姓名</view>
          <view class="flex-bd">
            <u-input border="none" placeholder="请输入" v-model="form.UserName"></u-input>
          </view>
        </view>
        <view class="item bor-b flex">
          <view class="flex-hd">手机号</view>
          <view class="flex-bd">
            <u-input border="none" v-model="form.CellPhone" :clearable="true"></u-input>
          </view>
        </view>
        <view class="item bor-b flex" v-if="UserInfo.UserType == 2">
          <view class="flex-hd">工号</view>
          <view class="flex-bd">
            <u-input border="none" v-model="form.JobNumber" :clearable="true"></u-input>
          </view>
        </view>
        <view class="item bor-b flex" @click="selectProject">
          <view class="flex-hd">项目</view>
          <view class="flex-bd">
            <u-input border="none" disabled disabledColor="#fff" v-model="form.ProjectName" placeholder="请选择"></u-input>
          </view>
          <view><u-icon name="arrow-right"></u-icon></view>
        </view>
      </view>
    </view>
    <view class="mr30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        CellPhone: uni.getStorageSync("UserInfo").CellPhone,
        UserName: uni.getStorageSync("UserInfo").UserName,
        ProjectCode: "",
        ProjectName: "",
        JobNumber: "",
      },
      UserInfo: uni.getStorageSync("UserInfo"),
    };
  },
  onLoad(options) {
    this.type = options.type;
  },
  onShow() {
    uni.$on("selectProject", (item) => {
      console.log(item);
      this.form.ProjectCode = item.Code;
      this.form.ProjectName = item.ProjectName;
      uni.$off("selectProject");
    });
  },
  methods: {
    selectProject() {
      uni.navigateTo({
        url: "../selectProject/selectProject",
      });
    },
    submit() {
      if (!this.form.UserName) {
        this.$u.toast("请输入姓名");
        return;
      }
      if (!this.form.CellPhone) {
        this.$u.toast("请输入手机号");
      }
      if (this.UserInfo.UserType == 2 && !this.form.JobNumber) {
        this.$u.toast("请输入工号");
        return;
      }
      if (!this.form.ProjectCode) {
        this.$u.toast("请选择项目");
        return;
      }
      this.$apis[this.UserInfo.UserType == 2 ? "ygAuth" : "yzAuth"]({
        ...this.form,
      }).then((res) => {
        if (res.code == 100) {
          uni.showToast({mask:true,
            title: "提交成功",
            icon: "success",
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);
        } else {
          this.$u.toast(res.msg);
        }
      });
    },
  },
};
</script>

<style></style>
