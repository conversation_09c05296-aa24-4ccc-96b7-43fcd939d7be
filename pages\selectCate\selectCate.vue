<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" :title="title" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="cate-path">
      <view class="path-item" v-for="(item, index) in path" :key="index" @click="goBack(index)">
        <view class="path-item-name">{{ item.CateName || item.LocationName }}</view>
        <view class="path-item-arrow" v-if="index !== path.length - 1">
          <u-icon name="arrow-right" color="#999" size="14"></u-icon>
        </view>
      </view>
    </view>
    <view class="cate-list">
      <view class="li flex bor-b" @click="select(item)" v-for="(item, index) in list" :key="index">
        <view class="flex-bd">{{ item.CateName || item.LocationName }}</view>
        <view class="flex-ft">
          <u-icon name="arrow-right" color="#999" size="14"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      title: "选择分类",
      type: "", // location 或 category
      parentCode: "",
      ProjectCode: "",
      ModuleName: "",
      item: {},
      path: [],
    };
  },
  onLoad(options) {
    this.ProjectCode = options.projectCode;
    this.ModuleName = options.moduleName;
    this.type = options.type || "category";
    this.title = this.type === "location" ? "选择位置" : "选择分类";
    this.loadData(true);
  },
  methods: {
    loadData(first) {
      const api = this.type === "location" ? this.$apis.getInspectLocationList : this.$apis.getInspectCategoryList;

      api({
        ProjectCode: this.ProjectCode,
        CurUserCode: uni.getStorageSync("UserInfo").Code || "",
        ModuleName: this.ModuleName,
        ParentCode: first ? "root" : this.parentCode,
      }).then((res) => {
        if (first) {
          this.parentCode = res.data[0].Code;
          this.path = [res.data[0]];
          this.loadData(false);
        }
        if (res.data.length === 0) {
          if (this.type === "category") {
            uni.$emit("selectCate", this.item);
          } else {
            uni.$emit("selectLocation", this.item);
          }
          setTimeout(() => {
            uni.navigateBack();
          }, 500);
        } else {
          if (!first) {
            this.list = res.data;
          }
        }
      });
    },
    select(item) {
      this.item = item;
      this.parentCode = item.Code;
      this.path.push(item);
      this.loadData(false);
    },
    goBack(index) {
      if (index >= 0) {
        this.path = this.path.slice(0, index + 1);
        this.parentCode = this.path[index].Code;
        this.loadData(false);
      }
    },
  },
};
</script>

<style lang="scss">
.cate-path {
  background: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border-bottom: 1px solid #eee;

  .path-item {
    display: flex;
    align-items: center;
    margin-right: 10rpx;
    margin-bottom: 10rpx;

    &-name {
      color: #666;
      font-size: 28rpx;

      &:active {
        opacity: 0.7;
      }
    }

    &-arrow {
      margin: 0 6rpx;
    }
  }
}

.cate-list {
  .li {
    padding: 30rpx;
    background: #fff;
    .flex-ft {
      margin-left: 20rpx;
    }
  }
}
</style>
