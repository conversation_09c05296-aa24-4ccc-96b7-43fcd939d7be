<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="工作日报" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">详情记录</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">填报日期</view>
          <view class="flex-bd">{{ form.DailyTime }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">填报人</view>
          <view class="flex-bd">{{ form.DailyUserName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">日报内容</view>
          <view class="flex-bd">{{ form.DailyContent }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      Code: "",
      fileList1: [],
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.$apis.getDailyDetail({ Code: this.Code }).then((res) => {
        if (res.data.Images) {
          this.fileList1 = res.data.Images.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        }
        this.form = res.data;
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
