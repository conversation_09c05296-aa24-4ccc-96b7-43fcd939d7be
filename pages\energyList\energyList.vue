<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="能耗管理" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <view class="search-box" @click="searchShow = true">
        <u-search disabled placeholder="搜索" :showAction="false" shape="square"></u-search>
      </view>
    </view>
    <view class="scroll" style="padding-top: 54px">
      <view class="repaire-list">
        <view class="li" @click="$c.naviTo('../energyDetail/energyDetail?Code=' + item.Code)" v-for="(item, index) in list">
          <view class="li-top flex">
            <view class="left flex-bd">表号:{{ item.MeterNumber }}</view>
          </view>
          <view class="li-bottom">
            <view class="flex">
              <view class="flex-hd">类型</view>
              <view class="flex-bd">{{ item.MeterType }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">抄表周期</view>
              <view class="flex-bd line-1">{{ item.CycleType == "按周期" ? item.InspectionTime + item.InspectionUnit : item.InspectionWeek + item.TimeRanges }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">抄表位置</view>
              <view class="flex-bd line-1">{{ item.Address }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">最近一次抄表</view>
              <view class="flex-bd line-1">{{ item.LastMeterTimeCaption }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <u-popup :show="searchShow" mode="top" bgColor="transparent" overlayOpacity="0.1" zIndex="10006" @close="searchShow = false">
      <view class="pop-select" :style="'padding-top: ' + statusBarHeight + 'px;'">
        <view class="title flex">
          <view class="flex-bd">筛选</view>
          <view class="flex-hd" @click="searchShow = false">
            <u-icon name="close" :size="20"></u-icon>
          </view>
        </view>
        <scroll-view class="pd030" :scroll-y="true" style="box-sizing: border-box">
          <view class="form">
            <u-form labelWidth="60">
              <!-- 状态 -->
              <u-form-item required label="状态" borderBottom>
                <view class="status-group">
                  <view v-for="(item, index) in statusList" :key="index" class="status-item" :class="{ active: Status === item.value }" @click="Status = item.value">
                    {{ item.name }}
                  </view>
                </view>
              </u-form-item>
              <!-- 表号 -->
              <u-form-item label="表号" borderBottom>
                <u-input v-model="MeterNumber" border="none" placeholder="表号" />
              </u-form-item>
              <!-- 位置	 -->
              <u-form-item required label="位置" borderBottom>
                <u-input v-model="Address" border="none" placeholder="请输入位置" />
              </u-form-item>
            </u-form>
          </view>
          <view style="padding: 50rpx 0">
            <view class="flex" style="gap: 30rpx">
              <u-button :ripple="true" :hairline="false" type="info" @click="searchShow = false">取消</u-button>
              <u-button :ripple="true" :hairline="false" :customStyle="'background: ' + $c.color() + ';color:#fff;border:0'" type="primary" @click="(popShow = false), search()">搜索</u-button>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchShow: false,
      statusBarHeight: 0,
      divHei: 0,
      scrollHei: 0,
      statusList: [
        { name: "全部", value: "" },
        { name: "已超期", value: 1 },
        { name: "未超期", value: 2 },
      ],
      MeterNumber: "",
      Address: "",
      Status: "",
      list: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      ProjectCode: "",
    };
  },
  onLoad(e) {
    var sys = uni.getSystemInfoSync();
    this.statusBarHeight = sys.statusBarHeight;
    this.divHei = sys.windowHeight - sys.statusBarHeight;
    this.scrollHei = sys.windowHeight - sys.statusBarHeight - 44;

    this.ProjectCode = e.ProjectCode;
    // 初始加载数据
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    search() {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
        this.searchShow = false;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getEnergyList(
            {
              PageIndex: page,
              PageSize: 10,
              MeterNumber: this.MeterNumber,
              Address: this.Address,
              Status: this.Status || "0",
              ProjectCode: this.ProjectCode,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style lang="scss">
page {
  background: #f6f6f6;
}
.repaire-list .li-bottom .flex-hd {
  width: 180rpx;
}
</style>
