<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="卤菜外卖" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">预约信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 申请人 -->
          <u-form-item label="申请人" borderBottom>
            <u-input v-model="form.GlobalUserName" border="none" disabledColor="#fff" :disabled="true" placeholder="申请人" />
          </u-form-item>
          <!-- 联系电话 -->
          <u-form-item required label="联系电话" borderBottom>
            <u-input v-model="form.CellPhone" border="none" placeholder="请输入联系电话" />
          </u-form-item>
          <!-- 取餐时间 -->
          <u-form-item required label="取餐时间" borderBottom @click="outTimeShow = true">
            <u-input v-model="form.OutTime" disabledColor="#fff" :disabled="true" border="none" placeholder="请选择取餐时间" />
            <u-icon name="arrow-right" slot="right"></u-icon>
          </u-form-item>
          <!-- 备注 -->
          <u-form-item label="备注">
            <u-textarea v-model="form.Remark" border="none" placeholder="请输入备注信息" />
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="form-pannel">
      <view class="pannel-title bor-b flex">
        <view class="title-content flex-bd">菜品信息</view>
        <view class="btn">
          <u-button size="mini" :ripple="true" :hairline="false" type="info" @click="addDish">添加菜品</u-button>
        </view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <view class="dish-list">
          <view class="li flex bor-b" @click="select(item)" v-for="(item, index) in form.TakeOutFoodList" style="padding: 30rpx 0">
            <view class="image">
              <image :src="$c.getFullImage(item.DishImgs)" v-if="item.DishImgs" mode="aspectFill"></image>
              <image src="../../static/images/dish.png" v-else mode="aspectFill"></image>
            </view>
            <view class="flex-bd">
              <view class="top flex">
                <view class="name flex-bd">{{ item.DishName }}</view>
              </view>
              <view class="desc">规格：{{ item.Specifications }}</view>
              <view class="desc">库存：{{ item.Stock }}</view>
            </view>
            <view class="dish-price">
              <u-number-box v-model="item.Number" :max="item.Stock"></u-number-box>
            </view>
          </view>
        </view>

        <view v-if="form.TakeOutFoodList.length == 0">
          <view class="empty">
            <image src="../../static/images/nores.png" mode="widthFix"></image>
            <view class="text">暂无菜品</view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
    <u-datetime-picker :show="outTimeShow" v-model="timestamp" mode="datetime" :minDate="minDate" @confirm="onTimeConfirm" @cancel="outTimeShow = false"></u-datetime-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      outTimeShow: false,
      form: {
        ProjectCode: uni.getStorageSync("UserInfo").ProjectCodes,
        GlobalUserName: uni.getStorageSync("UserInfo").UserName || "",
        GlobalUserCode: uni.getStorageSync("UserInfo").Code || "",
        CellPhone: uni.getStorageSync("UserInfo").CellPhone || "",
        OutTime: "",
        Remark: "",
        TakeOutFoodList: [],
      },
      timestamp: "",
      minDate: new Date().getTime(),
    };
  },
  onLoad(e) {
    if (e.ProjectCode) {
      this.form.ProjectCode = e.ProjectCode;
    }
  },
  onShow() {
    uni.$on("selectDish", (list) => {
      console.log(list);
      list.forEach((newDish) => {
        const existingDish = this.form.TakeOutFoodList.find((dish) => dish.DishCode === newDish.DishCode);
        if (existingDish) {
          existingDish.Number += newDish.Number;
          if (existingDish.Number > existingDish.Stock) {
            existingDish.Number = existingDish.Stock;
          }
        } else {
          this.form.TakeOutFoodList.push(newDish);
        }
      });
      uni.$off("selectDish");
    });
  },
  methods: {
    addDish() {
      uni.navigateTo({
        url: "/pages/dishList/dishList?ProjectCode=" + this.form.ProjectCode,
      });
    },
    onTimeConfirm(e) {
      this.timestamp = e.value;
      this.form.OutTime = this.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:ss");
      this.outTimeShow = false;
    },
    submit() {
      if (!this.formValidation()) return;
      this.$apis
        .addTakeoutApply(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
    formValidation() {
      if (!this.form.CellPhone) {
        this.$u.toast("请输入联系电话");
        return false;
      }

      if (!this.$u.test.mobile(this.form.CellPhone)) {
        this.$u.toast("请输入正确的联系电话");
        return false;
      }

      if (!this.form.OutTime) {
        this.$u.toast("请选择取餐时间");
        return false;
      }

      if (this.form.TakeOutFoodList.length == 0) {
        this.$u.toast("请添加菜品");
        return false;
      }

      return true;
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
