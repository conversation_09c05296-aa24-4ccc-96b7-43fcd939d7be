<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="资产详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">资产详情</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">资产名称</view>
          <view class="flex-bd">{{ form.AssetName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">资产类型</view>
          <view class="flex-bd">{{ form.AssetCate }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">品牌</view>
          <view class="flex-bd">{{ form.Brand }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">规格型号</view>
          <view class="flex-bd">{{ form.Specification }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">购买日期</view>
          <view class="flex-bd">{{ form.BuyDateCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">使用状态</view>
          <view class="flex-bd">{{ form.IsUse == "1" ? "在用" : "不在用" }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.Remark }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="$c.naviTo('../assetAdd/assetAdd?Code=' + Code)">修改</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      fileList1: [],
      Code: "",
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.getDetail(options.Code);
  },
  onShow() {
    uni.$on("refreshAssetDetail", () => {
      this.getDetail(this.Code);
      uni.$off("refreshAssetDetail");
    });
  },
  methods: {
    getDetail(code) {
      this.$apis.getAssetDetail({ Code: code }).then((res) => {
        if (res.data.AssetPic) {
          this.fileList1 = res.data.AssetPic.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        }
        this.form = res.data;
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
