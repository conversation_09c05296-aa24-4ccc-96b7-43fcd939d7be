<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="能耗管理" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">{{ form.MeterType }}信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">表号</view>
          <view class="flex-bd">{{ form.MeterNumber }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">类型</view>
          <view class="flex-bd">{{ form.MeterType }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">抄表周期</view>
          <view class="flex-bd">{{ form.CycleType == "按周期" ? form.InspectionTime + form.InspectionUnit : form.InspectionWeek + form.TimeRanges }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">抄表位置</view>
          <view class="flex-bd">{{ form.Address }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">最近一次抄表</view>
          <view class="flex-bd">{{ form.LastMeterTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">下次截止时间</view>
          <view class="flex-bd">{{ form.NextDeadlineTimeCaption }}</view>
        </view>
      </view>
    </view>

    <view class="time-search flex">
      <view class="title">历史记录</view>
      <view class="input flex-bd" @click="showCalendar = true">
        <uni-datetime-picker :border="false" :start="minDate" :end="maxDate" v-model="datetimerange" type="daterange" @change="calendarConfirm" @clear="calendarConfirm" rangeSeparator="至" />
      </view>
      <!-- <view class="btn">
        <u-button type="info" customStyle="height:35px;border:0;'" @click="search">搜索</u-button>
      </view> -->
    </view>

    <view class="repaire-list">
      <view class="li" @click="$c.naviTo('../energyCheckDetail/energyCheckDetail?Code=' + item.Code)" v-for="(item, index) in list">
        <view class="li-top flex">
          <view class="left flex-bd">抄表人:{{ item.ReadingUserName }}</view>
        </view>
        <view class="li-bottom">
          <view class="flex">
            <view class="flex-hd">抄表时间</view>
            <view class="flex-bd">{{ $c.formatDate(item.ReadingTime) }}</view>
          </view>
          <view class="flex">
            <view class="flex-hd">抄表数值</view>
            <view class="flex-bd">{{ item.UseValue }}</view>
          </view>
          <view class="flex flext">
            <view class="flex-hd">备注</view>
            <view class="flex-bd">{{ item.Remark }}</view>
          </view>
          <view class="flex flext" v-if="item.Images.length > 0">
            <view class="flex-hd">照片</view>
            <view class="flex-bd">
              <view class="image-list">
                <image v-for="(i, index) in item.Images" :src="i" mode="aspectFill" @click.stop="$c.viewImage(item.Images, index)"></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <view class="float-btn" @click="goAdd">
      <u-icon name="plus" color="#fff" size="24"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      Code: "",
      form: {
        MeterType: "",
        MeterNumber: "",
        CycleType: "",
        InspectionTime: "",
        InspectionUnit: "",
        InspectionWeek: "",
        TimeRanges: "",
      },
      list: [],
      showCalendar: false,
      startDate: "",
      endDate: "",
      datetimerange: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      minDate: "",
      maxDate: "",
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.getDetail();
    this.getList(1).then((res) => {
      this.list = res;
    });
    this.minDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1)).getTime();
    this.maxDate = new Date().getTime();
  },
  onShow() {
    uni.$on("refreshEnergyList", (data) => {
      if (this.sectionIndex == 0) {
        this.page = 1;
        this.list = [];
        this.getList(1).then((res) => {
          this.list = res;
        });
      }
      uni.$off("refreshEnergyList");
    });
  },
  methods: {
    calendarConfirm(e) {
      console.log(e);
      this.startDate = e[0];
      this.endDate = e[1];
      this.page = 1;
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    getDetail() {
      this.$apis.getEnergyDetail({ Code: this.Code }).then((res) => {
        this.form = res.data;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        let params = {
          MeterCode: this.Code,
          PageIndex: page,
          PageSize: 10,
        };
        if (this.startDate && this.endDate) {
          params.StartTime = this.startDate;
          params.EndTime = this.endDate;
        }
        this.$apis.getEnergyRecordList(params, { loading: false }).then((res) => {
          if (res.data.length < 10) {
            this.loadmore.status = "nomore";
          }
          if (res.data.length > 0) {
            for (let i in res.data) {
              if (res.data[i].Images) {
                var arr = res.data[i].Images.split(",");
                for (let j in arr) {
                  arr[j] = this.$c.getFullImage(arr[j]);
                }
                res.data[i].Images = arr;
              }
            }
          }
          resolve(res.data);
        });
      });
    },
    goAdd() {
      uni.navigateTo({
        url: `../energyCheckAdd/energyCheckAdd?Code=${this.Code}`,
      });
    },
    goDetail(item) {
      uni.navigateTo({
        url: `../energyCheckDetail/energyCheckDetail?Code=${item.Code}`,
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style lang="scss">
page {
  background: #f6f6f6;
}
.form-cells .label {
  width: 200rpx;
}
</style>
