<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="报事报修" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">上报信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 申请人 -->
          <u-form-item label="申请人" borderBottom>
            <u-input v-model="form.GlobalUserName" border="none" disabledColor="#fff" :disabled="true" placeholder="申请人" />
          </u-form-item>
          <!-- 联系电话 -->
          <u-form-item required label="联系电话" borderBottom>
            <u-input v-model="form.CellPhone" border="none" placeholder="请输入联系电话" />
          </u-form-item>
          <!-- 报修类型 -->
          <u-form-item required label="类型" borderBottom>
            <u-radio-group v-model="form.ApplyType" @groupChange="groupChange">
              <u-radio :activeColor="$c.color()" :customStyle="{ marginRight: '30rpx' }" v-for="(item, index) in typeList" :key="index" :label="item.label" :name="item.name"></u-radio>
            </u-radio-group>
          </u-form-item>
          <!-- 维修地点 -->
          <u-form-item required label="上报地点" borderBottom>
            <u-input v-model="form.Address" border="none" placeholder="请输入地点" />
          </u-form-item>
          <!-- 报修内容 -->
          <u-form-item required label="上报内容" borderBottom>
            <u-textarea v-model="form.RepairMatter" border="none" placeholder="请输入内容" />
          </u-form-item>
          <u-form-item label="图片">
            <my-upload v-model="fileList1" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        ProjectCode: uni.getStorageSync("UserInfo").ProjectCodes,
        GlobalUserName: uni.getStorageSync("UserInfo").UserName || "",
        GlobalUserCode: uni.getStorageSync("UserInfo").Code || "",
        CellPhone: uni.getStorageSync("UserInfo").CellPhone || "",
        Address: "",
        RepairMatter: "",
        Photos: "",
        ApplyType: -1, //1：报修，0：报事
      },
      fileList1: [],
      typeList: [
        { label: "报修", name: 1 },
        { label: "报事", name: 0 },
      ],
    };
  },
  onLoad(e) {
    if (e.ProjectCode) {
      this.form.ProjectCode = e.ProjectCode;
    }
  },
  methods: {
    groupChange(e) {
      console.log(e);
    },
    handleUploadChange(data) {
      // 直接使用change事件返回的urls更新表单
      this.form.Photos = data.urls;
    },
    submit() {
      if (!this.formValidation()) return;

      // 不需要再手动处理图片数据，因为handleUploadChange已经处理了
      this.$apis
        .addRepair(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              uni.navigateBack();
              uni.$emit("refreshRepairList", true);
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
    formValidation() {
      if (this.form.ApplyType == -1) {
        this.$u.toast("请选择报修类型");
        return false;
      }

      if (!this.form.Address) {
        this.$u.toast("请输入报修地点");
        return false;
      }

      if (!this.form.RepairMatter) {
        this.$u.toast("请输入报修内容");
        return false;
      }

      if (!this.form.CellPhone) {
        this.$u.toast("请输入联系电话");
        return false;
      }

      if (!this.$u.test.mobile(this.form.CellPhone)) {
        this.$u.toast("请输入正确的联系电话");
        return false;
      }

      return true;
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
