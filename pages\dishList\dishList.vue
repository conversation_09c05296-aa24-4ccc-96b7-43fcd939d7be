<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="选择菜品" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="dish-list">
      <view class="li flex bor-b" @click="select(item)" v-for="(item, index) in list">
        <view class="flex-hd">
          <u-icon name="checkmark-circle-fill" :color="item.selected ? $c.color() : '#ddd'" size="18"></u-icon>
        </view>
        <view class="image">
          <image :src="$c.getFullImage(item.DishImgs)" v-if="item.DishImgs" mode="aspectFill"></image>
          <image src="../../static/images/dish.png" v-else mode="aspectFill"></image>
        </view>
        <view class="flex-bd">
          <view class="top flex">
            <view class="name flex-bd">{{ item.DishName }}</view>
          </view>
          <view class="desc">规格：{{ item.Specifications }}</view>
          <view class="desc">库存：{{ item.Stock }}</view>
        </view>
      </view>
    </view>
    <view class="pd30" style="margin-top: 40rpx">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">确认</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
    };
  },
  onLoad(options) {
    this.$apis
      .getTakeoutDishList({
        ProjectCode: options.ProjectCode,
      })
      .then((res) => {
        res.data.forEach((item) => ((item.selected = false), (item.Number = 1), (item.DishCode = item.Code)));
        this.list = res.data;
      });
  },
  methods: {
    select(item) {
      item.selected = !item.selected;
    },
    submit() {
      let selectedList = this.list.filter((item) => item.selected);
      if (selectedList.length == 0) {
        this.$c.toast("请选择菜品");
        return;
      }
      uni.$emit("selectDish", selectedList);
      setTimeout(() => {
        uni.navigateBack();
      }, 100);
    },
  },
};
</script>

<style lang="scss"></style>
