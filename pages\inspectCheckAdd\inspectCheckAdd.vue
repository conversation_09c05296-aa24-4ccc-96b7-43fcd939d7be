<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" :title="moduleName" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">登记信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 名称 -->
          <u-form-item :label="label.EquipmentName" borderBottom>
            <u-input v-model="form.EquipmentName" disabledColor="#fff" disabled border="none" placeholder="请输入设备名称" />
          </u-form-item>
          <!-- 填写日期 -->
          <u-form-item label="填写日期" borderBottom>
            <u-input v-model="form.ReadingTime" disabledColor="#fff" disabled border="none" placeholder="请输入抄表日期" />
          </u-form-item>
          <!-- 巡检人 -->
          <u-form-item v-if="isLeader" :label="label.InspectorUserName" borderBottom @click="$c.selectPeople('inspectUser', 1, ProjectCode, ['CreateUserName', 'CreateUserCode'], { moduleName: moduleName })">
            <u-input v-model="form.CreateUserName" disabledColor="#fff" disabled border="none" placeholder="请输入巡检人" />
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <!-- 内容 -->
          <u-form-item :label="label.InspectionContent" borderBottom>
            <view class="radio-list">
              <view class="radio-items" v-for="item in InspectionRemarks">
                <view class="title">{{ item.Content }}</view>
                <view class="input" v-if="item.StandardValue">
                  <u-input v-model="item.FactValue" placeholder="请输入实际值" />
                </view>
                <view class="radio" v-else>
                  <view class="radio-item" @click="updateSelected(item, true)">
                    <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.selected ? $c.color() : '#ccc'"></u-icon></view>
                    <view class="inline">是</view>
                  </view>
                  <view class="radio-item" @click="updateSelected(item, false)">
                    <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.selected ? '#ccc' : $c.color()"></u-icon></view>
                    <view class="inline">否</view>
                  </view>
                </view>
              </view>
            </view>
          </u-form-item>
          <!-- 备注 -->
          <u-form-item label="备注" borderBottom>
            <u-textarea v-model="form.InspectionContent" border="none" placeholder="请输入备注信息" />
          </u-form-item>
          <!-- 图片上传 -->
          <u-form-item label="图片">
            <my-upload v-model="fileList" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showDatePicker: false,
      form: {
        CreateUserCode: uni.getStorageSync("UserCode"),
        CreateUserName: uni.getStorageSync("UserInfo").UserName,
        EquipmentCode: "", //设备编码
        EquipmentName: "", //设备名称
        InspectorUserCode: uni.getStorageSync("UserInfo").Code, //巡检人编号
        InspectionContent: "", //巡检内容
        Images: "", //巡检图片
        VideoPath: "0", //巡检视频
        LatLon: "0,0", //经纬度信息
        SignImage: "0", //签名
        ModuleName: "", //所属模块
        InspectionRemarks: [
          {
            Content: "测试(正常)",
            Result: "1",
            FactValue: "1",
          },
          {
            Content: "检查环境(正常)",
            Result: "2",
            FactValue: "2",
          },
        ],
      },
      fileList: [],
      moduleName: "",
      Code: "",
      label: {},
      InspectionRemarks: [],
      ProjectCode: "",
      isLeader: false,
    };
  },
  onLoad(options) {
    this.ProjectCode = options.ProjectCode;
    this.Code = options.Code;
    this.form.EquipmentCode = options.Code;
    this.moduleName = options.moduleName;
    this.label = this.$c.switchInspectionLabel(this.moduleName);
    this.form.ModuleName = options.moduleName;
    this.form.ReadingTime = this.$u.timeFormat(new Date(), "yyyy-mm-dd hh:MM:ss");
    this.$apis.getInspectDetail({ Code: options.Code }).then((res) => {
      this.form.EquipmentName = res.data.EquipmentName;
    });

    this.isLeader = uni.getStorageSync("UserInfo").CurRolesCaption.includes("领导");

    //获取位置
    uni.getLocation({
      type: "wgs84",
      success: (res) => {
        this.form.LatLon = res.latitude + "," + res.longitude;
      },
    });

    //获取检查标准
    this.$apis.getInspectStandardList({ EquipmentCode: this.Code }).then((res) => {
      for (let i in res.data) {
        res.data[i].selected = true;
        res.data[i].Result = 1;
        if (res.data[i].StandardValue) {
          res.data[i].FactValue = "";
        } else {
          res.data[i].FactValue = "null";
        }
      }
      this.InspectionRemarks = res.data;
    });
  },
  onShow() {
    uni.$on("selectPeople", (data) => {
      this.form.CreateUserCode = data.list[0].Code;
      this.form.CreateUserName = data.list[0].UserName;
      uni.$off("selectPeople");
    });
  },
  methods: {
    updateSelected(item, value) {
      this.$set(item, "selected", value);
      this.$set(item, "Result", value ? "1" : "0");
    },
    handleUploadChange(data) {
      this.form.Images = data.urls;
    },
    submit() {
      if (!this.form.ReadingTime) {
        this.$u.toast("请选择用电日期");
        return false;
      }

      // 检查标准
      for (let i in this.InspectionRemarks) {
        if (this.InspectionRemarks[i].StandardValue && !this.InspectionRemarks[i].FactValue) {
          this.$u.toast("请完成" + this.label.InspectionContent);
          return false;
        }
      }
      this.form.InspectionRemarks = this.InspectionRemarks.map((item) => {
        return {
          Content: item.Content,
          Result: item.Result,
          FactValue: item.FactValue,
        };
      });
      this.$apis
        .addInspectRecord(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              uni.navigateBack();
              uni.$emit("refreshInspectList", true);
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
