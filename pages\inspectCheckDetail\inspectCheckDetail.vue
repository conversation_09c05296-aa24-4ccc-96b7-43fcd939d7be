<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" :title="moduleName" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">详情记录</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">{{ label.EquipmentName }}</view>
          <view class="flex-bd">{{ form.EquipmentName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">{{ label.EquipmentNumber }}</view>
          <view class="flex-bd">{{ form.EquipmentNumber }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">{{ label.InspectionTime }}</view>
          <view class="flex-bd">{{ $c.formatTime(form.InspectionTime) }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">{{ label.InspectionContent }}</view>
          <view class="radio-list">
            <view class="radio-items" v-for="item in InspectionRemarks">
              <view class="title">{{ item.Content }}</view>
              <view class="input" v-if="item.FactValue != 'null'">
                <u-input v-model="item.FactValue" disabledColor="#fff" disabled placeholder="请输入实际值" />
              </view>
              <view class="radio" v-else>
                <view class="radio-item">
                  <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.Result == 1 ? $c.color() : '#ccc'"></u-icon></view>
                  <view class="inline">是</view>
                </view>
                <view class="radio-item">
                  <view class="inline"><u-icon name="checkmark-circle-fill" :size="18" :color="item.Result == 0 ? $c.color() : '#ccc'"></u-icon></view>
                  <view class="inline">否</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.InspectionContent }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">{{ label.Images }}</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      Code: "",
      fileList1: [],
      moduleName: "",
      label: {},
      InspectionRemarks: [],
    };
  },
  onLoad(options) {
    this.Code = options.Code;
    this.moduleName = options.moduleName;
    this.label = this.$c.switchInspectionLabel(this.moduleName);
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.$apis.getInspectRecordDetail({ Code: this.Code }).then((res) => {
        if (res.data.InspectionBizQuery.Images) {
          this.fileList1 = res.data.InspectionBizQuery.Images.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        }
        this.form = res.data.InspectionBizQuery;
        this.InspectionRemarks = res.data.InspectionRemarks;
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
