<template>
  <view class="my-upload">
    <u-upload :width="width" :height="height" :maxCount="maxCount" :fileList="fileList" multiple @afterRead="handleAfterRead" @delete="handleDelete" :name="name" :capture="['album', 'camera']" :sourceType="['album', 'camera']" :previewFullImage="true" :compressed="true" @error="handleError"></u-upload>
  </view>
</template>

<script>
export default {
  name: "my-upload",
  props: {
    // 图片宽度
    width: {
      type: [String, Number],
      default: 60,
    },
    // 图片高度
    height: {
      type: [String, Number],
      default: 60,
    },
    // 最大上传数量
    maxCount: {
      type: [String, Number],
      default: 10,
    },
    // 组件标识
    name: {
      type: [String, Number],
      default: "1",
    },
    // 双向绑定的值
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fileList: [],
      isUploading: false, // 添加上传状态标记
      lastUploadTime: 0, // 添加最后上传时间记录
    };
  },
  watch: {
    value: {
      handler(newVal) {
        // 外部更新value时，更新组件内的fileList
        if (JSON.stringify(newVal) !== JSON.stringify(this.fileList)) {
          this.fileList = JSON.parse(JSON.stringify(newVal));
        }
      },
      immediate: true,
      deep: true,
    },
    fileList: {
      handler(newVal) {
        // fileList更新时，通过事件通知父组件
        // 使用 nextTick 确保数据更新后再触发事件
        this.$nextTick(() => {
          this.$emit("input", JSON.parse(JSON.stringify(newVal)));
          // 同时触发change事件，传递文件列表和拼接后的url字符串
          const urls = newVal.map((item) => item.pic).join(",");
          this.$emit("change", {
            files: JSON.parse(JSON.stringify(newVal)),
            urls: urls,
          });
        });
      },
      deep: true,
    },
  },
  methods: {
    // 处理上传后的文件
    async handleAfterRead(event) {
      const now = Date.now();
      // 如果距离上次上传时间小于1秒，则忽略
      if (now - this.lastUploadTime < 1000) {
        console.log("忽略重复上传");
        return;
      }

      this.lastUploadTime = now;

      if (this.isUploading) {
        console.log("正在上传中，请稍后");
        return;
      }

      try {
        this.isUploading = true;
        await this.$c.handleUploadAfterRead(event, this.fileList);
        this.$emit("input", JSON.parse(JSON.stringify(this.fileList)));
      } catch (error) {
        console.error("文件上传失败:", error);
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
      } finally {
        setTimeout(() => {
          this.isUploading = false;
        }, 1000);
      }
    },
    // 处理删除文件
    handleDelete(event) {
      this.$c.handleUploadDelete(event, this.fileList);
    },
    // 获取当前文件列表
    getFileList() {
      return this.fileList;
    },
    // 获取拼接后的url字符串
    getUrls() {
      return this.fileList.map((item) => item.pic).join(",");
    },
    // 清空文件列表
    clear() {
      this.fileList = [];
    },
    // 处理上传错误
    handleError(error) {
      console.error("上传错误:", error);
      if (error.errMsg == "chooseImage:fail cancel") {
      } else {
        uni.showToast({
          title: "上传出错",
          icon: "none",
        });
      }
    },
  },
};
</script>

<style>
.my-upload {
  display: inline-block;
}
</style>
