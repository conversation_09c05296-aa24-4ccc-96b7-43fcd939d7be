<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="餐食预留" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">预约信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 申请人 -->
          <u-form-item label="申请人" borderBottom>
            <u-input v-model="form.GlobalUserName" border="none" disabledColor="#fff" :disabled="true" placeholder="申请人" />
          </u-form-item>
          <!-- 联系电话 -->
          <u-form-item required label="联系电话" borderBottom>
            <u-input v-model="form.CellPhone" border="none" placeholder="请输入联系电话" />
          </u-form-item>
          <!-- 用餐时间 -->
          <u-form-item required label="用餐时间" borderBottom @click="outTimeShow = true">
            <u-input v-model="form.ReserveTime" disabledColor="#fff" :disabled="true" border="none" placeholder="请选择用餐时间" />
            <u-icon name="arrow-right" slot="right"></u-icon>
          </u-form-item>
          <!-- 用餐人数 -->
          <u-form-item required label="用餐人数" borderBottom>
            <u-input v-model="form.Number" type="number" border="none" placeholder="请输入用餐人数" />
          </u-form-item>
          <!-- 备注 -->
          <u-form-item label="备注">
            <u-textarea v-model="form.Remark" border="none" placeholder="请输入备注信息" />
          </u-form-item>
        </u-form>
      </view>
    </view>

    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
    <u-datetime-picker :show="outTimeShow" v-model="timestamp" mode="datetime" :minDate="minDate" @confirm="onTimeConfirm" @cancel="outTimeShow = false"></u-datetime-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      outTimeShow: false,
      form: {
        ProjectCode: uni.getStorageSync("UserInfo").ProjectCodes,
        GlobalUserName: uni.getStorageSync("UserInfo").UserName || "",
        GlobalUserCode: uni.getStorageSync("UserInfo").Code || "",
        CellPhone: uni.getStorageSync("UserInfo").CellPhone || "",
        ReserveTime: "",
        Number: "",
        Remark: "",
      },
      timestamp: "",
      minDate: new Date().getTime(),
    };
  },
  onLoad(e) {
    if (e.ProjectCode) {
      this.form.ProjectCode = e.ProjectCode;
    }
  },
  methods: {
    onTimeConfirm(e) {
      this.timestamp = e.value;
      this.form.ReserveTime = this.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:ss");
      this.outTimeShow = false;
    },
    submit() {
      if (!this.formValidation()) return;
      this.$apis
        .addReserveApply(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
    formValidation() {
      if (!this.form.CellPhone) {
        this.$u.toast("请输入联系电话");
        return false;
      }
      if (!this.$u.test.mobile(this.form.CellPhone)) {
        this.$u.toast("请输入正确的联系电话");
        return false;
      }
      if (!this.form.ReserveTime) {
        this.$u.toast("请选择用餐时间");
        return false;
      }
      if (!this.form.Number) {
        this.$u.toast("请输入用餐人数");
        return false;
      }
      return true;
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
