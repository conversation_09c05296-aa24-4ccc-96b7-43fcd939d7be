<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="考勤管理" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">考勤信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <!-- 联系电话 -->
          <u-form-item required label="应到人数" borderBottom>
            <u-input v-model="form.ReachedNum" type="number" border="none" placeholder="请输入应到人数" />
          </u-form-item>
          <!-- 报修类型 -->
          <u-form-item required label="实到人数" borderBottom>
            <u-input v-model="form.PresentNum" type="number" border="none" placeholder="请输入实到人数" />
          </u-form-item>
          <!-- 维修地点 -->
          <u-form-item label="备注" borderBottom>
            <u-input v-model="form.Remark" border="none" placeholder="请输入备注" />
          </u-form-item>
          <u-form-item label="图片">
            <my-upload v-model="fileList1" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        ReachedNum: "", //应到多少人
        PresentNum: "", //实到多少人
        Smallimage: "", //申请人（必填）
        Remark: "", //申请部门（必填）
        CreateDateTime: "", //申请日期（必填）
        CreateUserCode: "", //登录人
      },
      fileList1: [],
      Code: "",
    };
  },
  onLoad(e) {
    this.Code = e.Code;
    if (this.Code) {
      this.$apis.getAttendanceDetail({ Code: this.Code }).then((res) => {
        this.form = res.data;
        this.fileList1 = res.data.Smallimage.split(",").map((item) => {
          return {
            url: this.$http.config.staticURL + item,
            pic: item,
          };
        });
      });
    }
    this.form.CreateUserCode = uni.getStorageSync("UserCode");
  },
  methods: {
    handleUploadChange(data) {
      // 直接使用change事件返回的urls更新表单
      this.form.Smallimage = data.urls;
    },
    submit() {
      if (!this.formValidation()) return;

      this.form.CreateDateTime = uni.$u.timeFormat(new Date(), "yyyy-mm-dd hh:MM:ss");
      console.log(this.form);

      // 不需要再手动处理图片数据，因为handleUploadChange已经处理了
      this.$apis[this.Code ? "updateAttendance" : "addAttendance"](this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });

            setTimeout(() => {
              uni.navigateBack();
              if (this.Code) {
                uni.$emit("refreshAttendanceDetail", true);
              } else {
                uni.$emit("refreshAttendanceList", true);
              }
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
    formValidation() {
      if (!this.form.ReachedNum) {
        this.$u.toast("请输入应到人数");
        return false;
      }

      if (!this.form.PresentNum) {
        this.$u.toast("请输入实到人数");
        return false;
      }
      return true;
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
