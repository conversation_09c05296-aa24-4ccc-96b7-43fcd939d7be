<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="通知公告" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="message-list">
      <view class="li flex" v-for="(item, index) in list" @click="gotoDetail(item)">
        <view class="icon icon4">
          <i class="iconfont icon-naozhong"></i>
        </view>
        <view class="flex-bd">
          <view class="top flex">
            <view class="title flex-bd">{{ item.Title }}通知</view>
            <view class="time">{{ item.CreateDatetimeCaption }}</view>
          </view>
          <view class="bor flex">
            <view class="con flex-bd line1">{{ item.NoticeContent }}</view>
            <view class="dot dot1" v-if="item.IsSee == 0"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      ProjectCode: "",
    };
  },
  onLoad(options) {
    this.ProjectCode = options.ProjectCode;
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    // 获取通知列表
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getNoticeList(
            {
              UserCode: uni.getStorageSync("UserCode"),
              PageIndex: page,
              PageSize: 10,
              ProjectCode: this.ProjectCode,
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            if (res.data.length > 0) {
              res.data.forEach((item) => {
                item.NoticeContent = this.$c.getText(item.NoticeContent);
              });
            }
            resolve(res.data);
          });
      });
    },

    // 查看通知详情
    gotoDetail(item) {
      uni.navigateTo({
        url: "../noticeDetail/noticeDetail?Code=" + item.Code,
      });
    },
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    let list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
