<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="添加资产" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="90">
          <u-form-item label="资产名称" borderBottom required>
            <u-input v-model="form.AssetName" border="none" placeholder="请输入资产名称" />
          </u-form-item>
          <u-form-item label="资产类型" borderBottom>
            <u-input v-model="form.AssetCate" border="none" placeholder="请输入资产类型" />
          </u-form-item>
          <u-form-item label="品牌" borderBottom>
            <u-input v-model="form.Brand" border="none" placeholder="请输入品牌" />
          </u-form-item>
          <u-form-item label="规格型号" borderBottom>
            <u-input v-model="form.Specification" border="none" placeholder="请输入规格型号" />
          </u-form-item>
          <u-form-item label="购买日期" borderBottom @click="showDatePicker = true">
            <u-input v-model="form.BuyDate" border="none" disabledColor="#fff" disabled placeholder="请输入购买日期" />
            <u-icon name="arrow-right" :size="20" slot="right"></u-icon>
          </u-form-item>
          <u-form-item label="使用状态" borderBottom>
            <u-radio-group v-model="form.IsUse">
              <u-radio :customStyle="{ marginRight: '15px' }" :activeColor="$c.color()" v-for="(item, index) in radioList" :key="index" :label="item.label" :name="item.name"></u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item label="备注" borderBottom>
            <u-textarea v-model="form.Remark" border="none" placeholder="请输入备注信息" />
          </u-form-item>
          <u-form-item label="图片">
            <my-upload v-model="fileList" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
    <u-datetime-picker :show="showDatePicker" v-model="timeValue" mode="date" @confirm="timeConfirm" @cancel="showDatePicker = false"></u-datetime-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showDatePicker: false,
      timeValue: Number(new Date()),
      form: {
        Code: "",
        AssetName: "",
        AssetCate: "",
        Brand: "",
        Specification: "",
        BuyDate: "",
        IsUse: -1,
        Remark: "",
        AssetPic: "",
        ProjectCode: "",
        UserCode: uni.getStorageSync("UserCode"),
      },
      radioList: [
        {
          name: 1,
          label: "在用",
        },
        {
          name: 0,
          label: "不在用",
        },
      ],
      fileList: [],
    };
  },
  onLoad(options) {
    this.form.ProjectCode = options.ProjectCode;
    this.form.Code = options.Code;
    if (options.Code) {
      this.$apis.getAssetDetail({ Code: this.form.Code }).then((res) => {
        if (res.data.AssetPic) {
          this.fileList = res.data.AssetPic.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        }
        res.data.BuyDate = this.$u.timeFormat(res.data.BuyDateCaption.replace("-", "/"), "yyyy-mm-dd");
        this.form = res.data;
      });
    }
  },
  methods: {
    timeConfirm(e) {
      this.form.BuyDate = this.$u.timeFormat(e.value, "yyyy-mm-dd");
      this.showDatePicker = false;
    },
    handleUploadChange(data) {
      this.form.AssetPic = data.urls;
    },
    submit() {
      if (!this.form.AssetName) {
        this.$u.toast("请输入资产名称");
        return;
      }
      var newUrls = [];
      for (let i = 0; i < this.fileList.length; i++) {
        newUrls.push(this.fileList[i].url.replace(this.$http.config.staticURL, ""));
      }
      this.form.AssetPic = newUrls.join(",");
      this.$apis[this.form.Code ? "updateAsset" : "addAsset"](this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({ mask: true, title: "提交成功", icon: "success", duration: 1500 });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
            if (this.form.Code) {
              uni.$emit("refreshAssetDetail");
            } else {
              uni.$emit("refreshAssetList");
            }
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch(() => {
          this.$u.toast("提交失败，请重试");
        });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
