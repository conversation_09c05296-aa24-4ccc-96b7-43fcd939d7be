<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="包厢预定详情" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title flex bor-b">
        <view class="title-content flex-bd">申请信息</view>
        <view class="status status1" v-if="form.ApplyStatus == 0">{{ form.ApplyStatusCaption }}</view>
        <view class="status status3" v-else-if="form.ApplyStatus == 2">{{ form.ApplyStatusCaption }}</view>
        <view class="status status2" v-else>{{ form.ApplyStatusCaption }}</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">申请单号</view>
          <view class="flex-bd">{{ form.BoxOrderNo }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">申请人</view>
          <view class="flex-bd">{{ form.ApplyUserName }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">申请时间</view>
          <view class="flex-bd">{{ form.ApplyTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">预定时间</view>
          <view class="flex-bd">{{ form.FromTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">用餐人数</view>
          <view class="flex-bd">{{ form.Number }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">用餐标准</view>
          <view class="flex-bd">{{ form.AvgMoney }}元/人</view>
        </view>
        <view class="form-item flex">
          <view class="label">总金额</view>
          <view class="flex-bd">{{ form.TotalMoney }}元</view>
        </view>
        <view class="form-item flex" @click="$c.callPhone(form.CellPhone)">
          <view class="label">联系电话</view>
          <view class="flex-bd">{{ form.CellPhone }}</view>
          <view class="flex-ft">
            <u-icon name="phone" :size="20" color="#999"></u-icon>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">备注</view>
          <view class="flex-bd">{{ form.Remark }}</view>
        </view>
      </view>
    </view>
    <block v-if="form.ApplyStatus > 0">
      <view class="form-pannel">
        <view class="pannel-title flex bor-b">
          <view class="title-content flex-bd">审核信息</view>
        </view>
        <view class="form form-cells" style="padding: 10rpx 0">
          <view class="form-item flex">
            <view class="label">审核结果</view>
            <view class="flex-bd">{{ form.ApplyStatus == 2 ? "不通过" : "通过" }}</view>
          </view>
          <view class="form-item flex">
            <view class="label">审核时间</view>
            <view class="flex-bd">{{ form.CheckTimeCaption }}</view>
          </view>
          <view class="form-item flex" v-if="form.ApplyStatus != 2">
            <view class="label">分配包厢</view>
            <view class="flex-bd">{{ form.BoxName }}</view>
          </view>
          <view class="form-item flex" v-if="form.ApplyStatus != 2">
            <view class="label">分配时间</view>
            <view class="flex-bd">{{ form.BoxFromTimeCaption }}</view>
          </view>
          <view class="form-item flex" v-if="form.ApplyStatus != 2">
            <view class="label">包厢联系人</view>
            <view class="flex-bd">{{ form.BoxContacts }}</view>
          </view>
          <view class="form-item flex" v-if="form.ApplyStatus != 2" @click="$c.callPhone(form.BoxContactsPhone)">
            <view class="label">联系人电话</view>
            <view class="flex-bd">{{ form.BoxContactsPhone }}</view>
            <view class="flex-ft">
              <u-icon name="phone" :size="20" color="#999"></u-icon>
            </view>
          </view>
          <view class="form-item flex">
            <view class="label">备注</view>
            <view class="flex-bd">{{ form.CheckRemark ? form.CheckRemark : "" }}</view>
          </view>
        </view>
      </view>
    </block>
    <!-- 管理员审核按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 0 && isAdmin">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="showCheckPopup = true">审核</u-button>
    </view>

    <!-- 管理员确认使用按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 1 && isAdmin">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="confirm">确认使用</u-button>
    </view>

    <!-- 用户取消预约按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 0 && UserCode == form.ApplyUserCode">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="cancel">取消预约</u-button>
    </view>

    <!-- 用户评价按钮 -->
    <view class="mr30" v-if="form.ApplyStatus == 3 && UserCode == form.ApplyUserCode && !form.EvaluateTimeCaption">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="$c.evaluate(Code, 'box')">评价</u-button>
    </view>

    <!-- 评价信息 -->
    <view class="form-pannel" v-if="form.EvaluateTimeCaption">
      <view class="pannel-title bor-b">
        <view class="title-content">评价信息</view>
      </view>
      <view class="form form-cells" style="padding: 10rpx 0">
        <view class="form-item flex">
          <view class="label">评价星级</view>
          <view class="flex-bd">
            <u-rate :count="form.EvaluateLevel" v-model="form.EvaluateLevel"></u-rate>
          </view>
        </view>
        <view class="form-item flex">
          <view class="label">评价时间</view>
          <view class="flex-bd">{{ form.EvaluateTimeCaption }}</view>
        </view>
        <view class="form-item flex">
          <view class="label">评价内容</view>
          <view class="flex-bd">{{ form.EvaluateContent }}</view>
        </view>
        <view class="form-item flex" v-if="fileList1.length > 0">
          <view class="label">图片</view>
          <view class="flex-bd">
            <u-upload name="1" multiple :maxCount="fileList1.length" :fileList="fileList1" :disabled="true" :deletable="false"></u-upload>
          </view>
        </view>
      </view>
    </view>

    <u-popup :show="showCheckPopup" mode="bottom" :closeable="true" @close="showCheckPopup = false">
      <view class="popup-content">
        <view class="form-pannel">
          <view class="pannel-title bor-b">
            <view class="title-content">审核</view>
          </view>
          <view class="form" style="padding: 10rpx 0">
            <u-form labelWidth="120">
              <u-form-item required label="审核结果" borderBottom>
                <u-radio-group v-model="checkForm.ApplyStatus">
                  <u-radio :activeColor="$c.color()" :name="1" customStyle="margin-right: 20rpx">通过</u-radio>
                  <u-radio :activeColor="$c.color()" :name="2" customStyle="margin-right: 20rpx">不通过</u-radio>
                </u-radio-group>
              </u-form-item>
              <!-- 通过时显示以下字段 -->
              <u-form-item required label="安排包厢" borderBottom v-if="checkForm.ApplyStatus == 1">
                <u-input v-model="checkForm.BoxName" border="none" placeholder="请输入包厢名称" />
              </u-form-item>
              <u-form-item required label="分配时间" borderBottom @click="boxTimeShow = true" v-if="checkForm.ApplyStatus == 1">
                <u-input v-model="checkForm.BoxFromTime" border="none" :disabled="true" disabledColor="#fff" placeholder="请选择分配时间" />
                <u-icon name="arrow-right" slot="right"></u-icon>
              </u-form-item>
              <u-form-item required label="包厢联系人" borderBottom v-if="checkForm.ApplyStatus == 1">
                <u-input v-model="checkForm.BoxContacts" border="none" placeholder="请输入包厢联系人" />
              </u-form-item>
              <u-form-item required label="联系人电话" borderBottom v-if="checkForm.ApplyStatus == 1">
                <u-input v-model="checkForm.BoxContactsPhone" border="none" placeholder="请输入联系人电话" />
              </u-form-item>
              <u-form-item label="备注" borderBottom>
                <u-textarea v-model="checkForm.CheckRemark" border="none" placeholder="请输入备注" />
              </u-form-item>
            </u-form>
          </view>
        </view>
        <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="check">确认</u-button>
      </view>
    </u-popup>

    <!-- 添加时间选择器 -->
    <u-datetime-picker :show="boxTimeShow" v-model="boxTimestamp" mode="datetime" :minDate="minDate" @confirm="onBoxTimeConfirm" @cancel="boxTimeShow = false"></u-datetime-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      fileList1: [],
      showCheckPopup: false,
      checkForm: {
        Code: "",
        ApplyStatus: "", //1通过，2不通过
        GlobalUserCode: uni.getStorageSync("UserCode"),
        CheckRemark: "",
        BoxName: "",
        BoxFromTime: "",
        BoxContacts: "",
        BoxContactsPhone: "",
      },
      UserCode: uni.getStorageSync("UserCode"),
      UserInfo: uni.getStorageSync("UserInfo"),
      Code: "",
      form: {},
      isAdmin: false,
      boxTimeShow: false,
      boxTimestamp: "",
      minDate: Number(new Date()),
    };
  },
  onShow() {
    uni.$on("evaluateRefresh", (res) => {
      if (res) {
        this.getDetail();
      }
      uni.$off("evaluateRefresh");
    });
  },
  onLoad(options) {
    this.Code = options.Code;
    this.checkForm.Code = this.Code;
    this.checkForm.BoxContacts = this.UserInfo.UserName;
    this.checkForm.BoxContactsPhone = this.UserInfo.CellPhone;
    this.isAdmin = this.UserInfo.CurRolesCaption.split(",").includes("包厢预定服务人员");
    this.getDetail();
  },
  methods: {
    getDetail() {
      this.$apis
        .getBoxApplyDetail({
          Code: this.Code,
        })
        .then((res) => {
          this.form = res.data;
          this.fileList1 = res.data.EvaluatePhotos.split(",").map((url) => ({
            url: this.$http.config.staticURL + url,
          }));
        });
    },
    check() {
      if (!this.checkForm.ApplyStatus) {
        this.$u.toast("请选择审核结果");
        return;
      }
      if (this.checkForm.ApplyStatus == 1) {
        if (!this.checkForm.BoxName) {
          this.$u.toast("请输入包厢名称");
          return;
        }
        if (!this.checkForm.BoxFromTime) {
          this.$u.toast("请选择分配时间");
          return;
        }
        if (!this.checkForm.BoxContacts) {
          this.$u.toast("请输入包厢联系人");
          return;
        }
        if (!this.checkForm.BoxContactsPhone) {
          this.$u.toast("请输入联系人电话");
          return;
        }
        if (!this.$u.test.mobile(this.checkForm.BoxContactsPhone)) {
          this.$u.toast("请输入正确的联系人电话");
          return;
        }
      }
      this.showCheckPopup = false;
      uni.showModal({
        title: "提示",
        content: "确定提交吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis.checkBoxApply(this.checkForm).then((res) => {
              if (res.code == 100) {
                uni.showToast({ mask: true, title: "审核成功", icon: "success" });
                setTimeout(() => {
                  this.getDetail();
                }, 1500);
              } else {
                this.$u.toast(res.msg);
              }
            });
          }
        },
      });
    },
    confirm() {
      uni.showModal({
        title: "提示",
        content: "确定使用吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .completeBox({
                Code: this.Code,
                GlobalUserCode: this.UserCode,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "确认成功", icon: "success" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
    cancel() {
      uni.showModal({
        title: "提示",
        content: "确定取消预约吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .cancelBoxApply({
                Code: this.Code,
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.showToast({ mask: true, title: "取消成功", icon: "success" });
                  setTimeout(() => {
                    this.getDetail();
                  }, 1500);
                } else {
                  this.$u.toast(res.msg);
                }
              });
          }
        },
      });
    },
    onBoxTimeConfirm(e) {
      this.boxTimestamp = e.value;
      this.checkForm.BoxFromTime = this.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM");
      this.boxTimeShow = false;
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
