<template>
  <view>
    <u-navbar :autoBack="false" :placeholder="true" leftIconColor="#fff" title="通讯录" :bgColor="$c.color()" titleStyle="color:#fff">
      <view slot="left"></view>
    </u-navbar>
    <view class="fix-tab">
      <view class="search-box">
        <u-search v-model="keyword" @change="searchChange" placeholder="搜索" :showAction="false" shape="square"></u-search>
      </view>
    </view>
    <view class="scroll" style="padding-top: 64px">
      <view class="contact" v-for="(item, index) in list">
        <view :class="'li flex ' + (index == 0 ? '' : 'bor-t')">
          <image class="avatar" src="../../static/images/team-n.png"></image>
          <view class="flex-bd" @click="$c.copy(item.CellPhone, '手机号码复制成功')">
            <view class="name flex">
              <view class="tag inline">{{ item.UserTypeCaption }}</view>
              <view class="flex-bd">{{ item.UserName }}</view>
            </view>
            <view class="role">
              <view class="role-item" v-for="(i, index) in item.CurRolesCaption">{{ i }}</view>
            </view>
          </view>
          <view class="tel" @click="call(item.CellPhone)">
            <view class="inline"><u-icon name="phone-fill" size="24" color="#fff"></u-icon></view>
          </view>
        </view>
      </view>
      <view class="pd30">
        <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
      </view>
    </view>
    <!-- 添加u-tabbar -->
    <u-tabbar :value="current" :fixed="true" @change="tabbarChange" :activeColor="$c.color()" :placeholder="true" :safeAreaInsetBottom="true">
      <u-tabbar-item text="首页" icon="home"></u-tabbar-item>
      <u-tabbar-item text="通讯录" icon="phone"></u-tabbar-item>
      <u-tabbar-item text="我的" icon="account"></u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
export default {
  data() {
    return {
      current: 1,
      keyword: "",
      list: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      timer: null, // 用于节流的定时器
    };
  },
  onLoad() {
    uni.hideTabBar();
    this.getContactList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    // 获取通讯录列表
    getContactList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getContactsList({
            PageIndex: page,
            PageSize: 10,
            UserName: this.keyword,
          })
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            if (res.data.length > 0) {
              for (let index in res.data) {
                if (!res.data[index].CurRolesCaption) {
                  res.data[index].CurRolesCaption = ["暂无角色"];
                } else {
                  res.data[index].CurRolesCaption = res.data[index].CurRolesCaption.split(",");
                }
              }
            }
            resolve(res.data);
          });
      });
    },

    // 搜索 - 添加节流
    searchChange(value) {
      // 清除之前的定时器
      if (this.timer) {
        clearTimeout(this.timer);
      }

      this.keyword = value;

      // 如果关键词为空，立即查询
      if (!value) {
        this.list = [];
        this.page = 1;
        this.loadmore.status = "loading";
        this.getContactList(1).then((res) => {
          this.list = res;
        });
        return;
      }

      // 设置700ms的节流延迟
      this.timer = setTimeout(() => {
        this.page = 1;
        this.list = [];
        this.loadmore.status = "loading";
        this.getContactList(1).then((res) => {
          this.list = res;
        });
      }, 700);
    },

    // 拨打电话
    call(phone) {
      if (!phone) {
        uni.showToast({ mask: true, title: "无效的电话号码", icon: "none" });
        return;
      }
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: () => {
          uni.showToast({ mask: true, title: "拨打电话失败", icon: "none" });
        },
      });
    },

    tabbarChange(index) {
      const tabList = ["../index/index", "../contacts/contacts", "../user/user"];
      uni.switchTab({
        url: tabList[index],
      });
    },
  },

  // 组件销毁时清除定时器
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.loadmore.status === "nomore") {
      return;
    }
    this.loadmore.status = "loading";
    let list = this.list;
    this.getContactList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
