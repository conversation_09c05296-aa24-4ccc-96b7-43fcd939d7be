<template>
  <view class="userInfo">
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="个人信息" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>

    <view class="u-info">
      <view class="avatar" @click="choose">
        <image class="avatar" v-if="form.HeadImg" :src="$c.getFullImage(form.HeadImg)"></image>
        <image class="avatar" v-else src="../../static/images/user-avatar1.png" mode="aspectFill"></image>
        <view class="edit">上传头像</view>
      </view>
      <view class="form">
        <view class="item bor-b flex">
          <view class="flex-hd">姓名</view>
          <view class="flex-bd">
            <u-input border="none" placeholder="请输入" v-model="form.UserName"></u-input>
          </view>
        </view>
        <view class="item bor-b flex" @click="sexShow = true">
          <view class="flex-hd">性别</view>
          <view class="flex-bd">
            <u-input border="none" disabled disabledColor="#fff" v-model="form.Gender" placeholder="请选择"></u-input>
          </view>
          <view><u-icon name="arrow-right"></u-icon></view>
        </view>
      </view>
    </view>
    <u-action-sheet :actions="sexList" title="请选择性别" :show="sexShow" @select="select"></u-action-sheet>
    <view class="mr30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      user: {},
      sexList: [
        {
          name: "男",
        },
        {
          name: "女",
        },
      ],
      sexShow: false,
      value1: Number(new Date()),
      dateShow: false,
      form: {
        Code: uni.getStorageSync("UserInfo").Code, //用户编号
        UserName: uni.getStorageSync("UserInfo").UserName, //真实姓名
        Gender: uni.getStorageSync("UserInfo").Gender, //性别
        JobNumber: uni.getStorageSync("UserInfo").JobNumber, //员工工号
        HeadImg: uni.getStorageSync("UserInfo").HeadImg, //头像
      },
    };
  },
  methods: {
    select(e) {
      this.sexShow = false;
      this.form.Gender = e.name;
    },
    choose() {
      uni.chooseImage({
        count: 1, //默认9
        sizeType: ["original", "compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], //从相册选择
        success: (res) => {
          // 上传图片
          this.$http
            .upload(
              "/PMSWebApi/Config/Upload",
              {
                filePath: res.tempFilePaths[0],
                name: "file",
              },
              { custom: { loading: false } }
            )
            .then((uploadRes) => {
              if (uploadRes.code === 100) {
                // 更新表单数据
                this.form.HeadImg = uploadRes.data.ServerFileName;
                uni.showToast({mask:true,
                  title: "上传成功",
                  icon: "success",
                });
              } else {
                this.$u.toast("上传失败");
              }
            })
            .catch((err) => {
              console.error("Upload failed:", err);
              this.$u.toast("上传失败");
            });
        },
        fail: (err) => {
          console.error("Choose image failed:", err);
        },
      });
    },
    submit() {
      if (!this.form.UserName) {
        this.$u.toast("请输入姓名");
        return;
      }

      // 提交用户信息
      this.$apis
        .updateUserInfo(this.form)
        .then((res) => {
          if (res.code === 100) {
            // 更新本地存储的用户信息
            const userInfo = uni.getStorageSync("UserInfo");
            Object.assign(userInfo, {
              UserName: this.form.UserName,
              Gender: this.form.Gender,
              HeadImg: this.form.HeadImg,
            });
            uni.setStorageSync("UserInfo", userInfo);

            uni.showToast({mask:true,
              title: "保存成功",
              icon: "success",
            });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            this.$u.toast(res.msg || "保存失败");
          }
        })
        .catch((err) => {
          console.error("Update failed:", err);
          this.$u.toast("保存失败，请重试");
        });
    },
  },
};
</script>

<style lang="scss"></style>
