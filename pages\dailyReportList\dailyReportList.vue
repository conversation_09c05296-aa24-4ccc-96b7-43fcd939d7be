<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="工作日报" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="fix-tab">
      <view class="search-box" @click="searchShow = true">
        <u-search disabled placeholder="搜索" :showAction="false" shape="square"></u-search>
      </view>
    </view>
    <view class="scroll" style="padding-top: 54px">
      <view class="repaire-list">
        <view class="li" @click="$c.naviTo('../dailyReportDetail/dailyReportDetail?Code=' + item.Code)" v-for="(item, index) in list">
          <view class="li-top flex">
            <view class="left flex-bd">{{ item.DailyUserName }}</view>
          </view>
          <view class="li-bottom">
            <view class="flex">
              <view class="flex-hd">填写日期</view>
              <view class="flex-bd">{{ $c.formatDate(item.DailyTime) }}</view>
            </view>
            <view class="flex">
              <view class="flex-hd">日报内容</view>
              <view class="flex-bd line-1">{{ item.DailyContent }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <u-loadmore fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <u-popup :show="searchShow" mode="top" bgColor="transparent" overlayOpacity="0.1" zIndex="10006" @close="searchShow = false">
      <view class="pop-select" :style="'padding-top: ' + statusBarHeight + 'px;'">
        <view class="title flex">
          <view class="flex-bd">筛选</view>
          <view class="flex-hd" @click="searchShow = false">
            <u-icon name="close" :size="20"></u-icon>
          </view>
        </view>
        <scroll-view class="pd030" :scroll-y="true" style="box-sizing: border-box">
          <view class="form">
            <u-form labelWidth="80">
              <!-- 填报人 -->
              <u-form-item label="填报人" borderBottom v-if="isAdmin">
                <u-input v-model="DailyUserName" border="none" placeholder="填报人" />
              </u-form-item>
              <!-- 填报日期 -->
              <u-form-item required label="填报日期" borderBottom @click="showDatePicker = true">
                <u-input v-model="DailyTime" disabled disabledColor="#fff" border="none" placeholder="请选择" />
                <u-icon name="arrow-right" :size="20" slot="right"></u-icon>
              </u-form-item>
            </u-form>
          </view>
          <view style="padding: 50rpx 0">
            <view class="flex" style="gap: 30rpx">
              <u-button :ripple="true" :hairline="false" type="info" @click="searchShow = false">取消</u-button>
              <u-button :ripple="true" :hairline="false" :customStyle="'background: ' + $c.color() + ';color:#fff;border:0'" type="primary" @click="(popShow = false), search()">搜索</u-button>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
    <u-datetime-picker :show="showDatePicker" v-model="timeValue" mode="date" @confirm="timeConfirm" @cancel="showDatePicker = false"></u-datetime-picker>
    <view class="float-btn" @click="$c.naviTo('../dailyReportAdd/dailyReportAdd?ProjectCode=' + ProjectCode)">
      <u-icon name="plus" color="#fff" size="24"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchShow: false,
      statusBarHeight: 0,
      divHei: 0,
      scrollHei: 0,
      DailyTime: "", //填报时间
      DailyUserName: "", //填报人
      timeValue: Number(new Date()),
      showDatePicker: false,
      list: [],
      page: 1,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      ProjectCode: "",
      isAdmin: false,
    };
  },
  onLoad(options) {
    this.ProjectCode = options.ProjectCode;
    var sys = uni.getSystemInfoSync();
    this.statusBarHeight = sys.statusBarHeight;
    this.divHei = sys.windowHeight - sys.statusBarHeight;
    this.scrollHei = sys.windowHeight - sys.statusBarHeight - 44;

    this.isAdmin = uni.getStorageSync("ProjectChargeUserCode") == uni.getStorageSync("UserCode");

    // 初始加载数据
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  onShow() {
    uni.$on("refreshDailyReportList", () => {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
      uni.$off("refreshDailyReportList");
    });
  },
  methods: {
    timeConfirm(e) {
      this.DailyTime = this.$u.timeFormat(e.value, "yyyy-mm-dd");
      this.showDatePicker = false;
    },
    search() {
      this.page = 1;
      this.list = [];
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
        this.searchShow = false;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getDailyList(
            {
              ProjectCode: this.ProjectCode, //项目编号
              CurUserCode: this.isAdmin ? "" : uni.getStorageSync("UserCode"), //当前登入人
              DailyTime: this.DailyTime,
              DailyUserName: this.DailyUserName,
              DailyUserCode: "",
            },
            { loading: false }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
};
</script>

<style lang="scss">
page {
  background: #f6f6f6;
}
.repaire-list .li-bottom .flex-hd {
  width: 180rpx;
}
</style>
