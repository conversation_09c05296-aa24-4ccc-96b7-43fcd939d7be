<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="评价" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">评价信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="70">
          <!-- 评价星级 -->
          <u-form-item required label="总体评价" borderBottom>
            <u-rate v-model="form.EvaluateLevel" count="5" />
          </u-form-item>
          <!-- 维修质量 -->
          <u-form-item required label="维修质量" borderBottom v-if="type == 'repair'">
            <u-radio-group v-model="form.RepairQuality">
              <u-radio :labelDisabled="false" :activeColor="$c.color()" customStyle="margin-right: 20rpx" v-for="(item, index) in repairQualityOptions" :key="index" :name="item">{{ item }}</u-radio>
            </u-radio-group>
          </u-form-item>
          <!-- 服务质量 -->
          <u-form-item required label="服务质量" borderBottom v-if="type == 'repair'">
            <u-radio-group v-model="form.ServiceQuality">
              <u-radio :labelDisabled="false" :activeColor="$c.color()" customStyle="margin-right: 20rpx" v-for="(item, index) in serviceQualityOptions" :key="index" :name="item">{{ item }}</u-radio>
            </u-radio-group>
          </u-form-item>
          <!-- 评价内容 -->
          <u-form-item required label="评价内容" borderBottom>
            <u-textarea v-model="form.EvaluateContent" placeholder="请输入评价内容" border="none" />
          </u-form-item>
          <!-- 评价图片 -->
          <u-form-item label="图片">
            <my-upload v-model="fileList" @change="handleUploadChange"></my-upload>
          </u-form-item>
        </u-form>
      </view>
    </view>
    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交评价</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        Code: "",
        EvaluateLevel: 5,
        EvaluateContent: "",
        EvaluatePhotos: "",
        RepairQuality: "满意",
        ServiceQuality: "满意",
      },
      fileList: [],
      repairQualityOptions: ["满意", "基本满意", "不满意"],
      serviceQualityOptions: ["满意", "不满意"],
      type: "",
    };
  },
  onLoad(options) {
    if (options.Code) {
      this.form.Code = options.Code;
    }
    if (options.Type) {
      this.type = options.Type;
    }
  },
  methods: {
    handleUploadChange(data) {
      this.form.EvaluatePhotos = data.urls;
    },
    formValidation() {
      if (!this.form.EvaluateContent) {
        this.$u.toast("请输入评价内容");
        return false;
      }
      if (!this.form.RepairQuality) {
        this.$u.toast("请选择维修质量评价");
        return false;
      }
      if (!this.form.ServiceQuality) {
        this.$u.toast("请选择服务质量评价");
        return false;
      }
      return true;
    },
    submit() {
      if (!this.formValidation()) return;

      this.$apis[this.switchType()](this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({mask:true,
              title: "评价成功",
              icon: "success",
              duration: 1500,
            });
            setTimeout(() => {
              uni.$emit("evaluateRefresh", true);
              uni.navigateBack();
            }, 1500);
          } else {
            this.$u.toast(res.msg || "评价失败");
          }
        })
        .catch((err) => {
          this.$u.toast("评价失败，请重试");
          console.error(err);
        });
    },
    switchType() {
      if (this.type === "takeout") {
        return "evaluateTakeout";
      }
      if (this.type === "repair") {
        return "evaluateRepair";
      }
      if (this.type === "reserve") {
        return "evaluateReserve";
      }
      if (this.type === "box") {
        return "evaluateBox";
      }
      if (this.type === "meeting") {
        return "evaluateMeeting";
      }
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
