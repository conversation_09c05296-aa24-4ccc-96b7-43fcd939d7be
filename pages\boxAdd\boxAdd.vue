<template>
  <view>
    <u-navbar :autoBack="true" :placeholder="true" leftIconColor="#fff" title="包厢预定" :bgColor="$c.color()" titleStyle="color:#fff"></u-navbar>
    <view class="form-pannel">
      <view class="pannel-title bor-b">
        <view class="title-content">预约信息</view>
      </view>
      <view class="form" style="margin: 0 40rpx">
        <u-form labelWidth="100">
          <!-- 申请人 -->
          <u-form-item label="申请人" borderBottom>
            <u-input v-model="form.GlobalUserName" border="none" disabledColor="#fff" :disabled="true" placeholder="申请人" />
          </u-form-item>
          <!-- 联系电话 -->
          <u-form-item required label="联系电话" borderBottom>
            <u-input v-model="form.CellPhone" border="none" placeholder="请输入联系电话" />
          </u-form-item>
          <!-- 预定时间 -->
          <u-form-item required label="预定时间" borderBottom @click="outTimeShow = true">
            <u-input v-model="form.FromTime" disabledColor="#fff" :disabled="true" border="none" placeholder="请选择预定时间" />
            <u-icon name="arrow-right" slot="right"></u-icon>
          </u-form-item>
          <!-- 用餐人数 -->
          <u-form-item required label="用餐人数" borderBottom>
            <u-input v-model="form.Number" type="number" border="none" placeholder="请输入用餐人数" />
          </u-form-item>
          <!-- 用餐标准 -->
          <u-form-item label="人均用餐标准" borderBottom>
            <u-input v-model="form.AvgMoney" type="number" border="none" placeholder="请输入人均用餐标准" />
            <view class="unit" slot="right">元/人</view>
          </u-form-item>
          <!-- 总金额 -->
          <u-form-item label="总金额" borderBottom>
            <u-input v-model="form.TotalMoney" type="number" border="none" placeholder="请输入总金额" />
            <view class="unit" slot="right">元</view>
          </u-form-item>
          <!-- 备注 -->
          <u-form-item label="备注">
            <u-textarea v-model="form.Remark" border="none" placeholder="请输入备注信息" />
          </u-form-item>
        </u-form>
      </view>
    </view>

    <view class="pd30">
      <u-button :ripple="true" :hairline="false" type="primary" :customStyle="$c.btnStyle()" @click="submit">提交</u-button>
    </view>
    <u-datetime-picker :show="outTimeShow" v-model="timestamp" mode="datetime" :minDate="minDate" @confirm="onTimeConfirm" @cancel="outTimeShow = false"></u-datetime-picker>
  </view>
</template>

<script>
export default {
  data() {
    return {
      outTimeShow: false,
      form: {
        ProjectCode: uni.getStorageSync("UserInfo").ProjectCodes,
        GlobalUserCode: uni.getStorageSync("UserInfo").Code || "",
        GlobalUserName: uni.getStorageSync("UserInfo").UserName || "",
        CellPhone: uni.getStorageSync("UserInfo").CellPhone || "",
        FromTime: "",
        Number: "",
        AvgMoney: "",
        TotalMoney: "",
        Remark: "",
      },
      timestamp: "",
      minDate: new Date().getTime(),
    };
  },
  methods: {
    onTimeConfirm(e) {
      this.timestamp = e.value;
      this.form.FromTime = this.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:ss");
      this.outTimeShow = false;
    },
    submit() {
      if (!this.formValidation()) return;
      this.$apis
        .addBoxApply(this.form)
        .then((res) => {
          if (res.code == 100) {
            uni.showToast({mask:true,
              title: "提交成功",
              icon: "success",
              duration: 1500,
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            this.$u.toast(res.msg || "提交失败");
          }
        })
        .catch((err) => {
          this.$u.toast("提交失败，请重试");
          console.error(err);
        });
    },
    formValidation() {
      if (!this.form.CellPhone) {
        this.$u.toast("请输入联系电话");
        return false;
      }
      if (!this.$u.test.mobile(this.form.CellPhone)) {
        this.$u.toast("请输入正确的联系电话");
        return false;
      }
      if (!this.form.FromTime) {
        this.$u.toast("请选择预定时间");
        return false;
      }
      if (!this.form.Number) {
        this.$u.toast("请输入用餐人数");
        return false;
      }

      if (!this.form.AvgMoney && !this.form.TotalMoney) {
        this.$u.toast("请输入人均用餐标准或总金额");
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss">
page {
  background: #f6f6f6;
}
.unit {
  color: #666;
  margin-left: 10rpx;
}
</style>
