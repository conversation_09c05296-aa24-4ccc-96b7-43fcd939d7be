<template>
  <view>
    <u-navbar :placeholder="true" leftIconColor="#fff" title="用户中心" :bgColor="$c.color()" titleStyle="color:#fff">
      <view slot="left"></view>
    </u-navbar>
    <view class="u-info flex">
      <image v-if="UserInfo.HeadImg" :src="$c.getFullImage(UserInfo.HeadImg)"></image>
      <image v-else src="../../static/images/user-avatar1.png"></image>
      <view class="flex-bd">
        <view class="name">{{ UserInfo.UserName }}</view>
        <view class="desc">欢迎来到管理系统</view>
      </view>
      <view @click="auth" :class="'tag ' + (UserInfo.IsCheck == 2 ? 'tag-success' : 'tag-error')">{{ UserInfo.IsCheck == 2 ? "已认证" : "待认证" }}</view>
    </view>
    <view class="user-cells">
      <view class="i flex bor-b" @click="$c.naviTo('../userInfo/userInfo')">
        <view class="icon">
          <image src="../../static/images/yonghu.png"></image>
        </view>
        <view class="flex-bd">个人信息</view>
        <u-icon name="arrow-right" :size="14" color="#999"></u-icon>
      </view>
      <view class="i flex bor-b" @click="$c.naviTo('../changePhone/changePhone')">
        <view class="icon">
          <image src="../../static/images/shouji.png"></image>
        </view>
        <view class="flex-bd">更换手机</view>
        <u-icon name="arrow-right" :size="14" color="#999"></u-icon>
      </view>
      <view class="i flex bor-b" @click="$c.naviTo('../bindOpenid/bindOpenid')">
        <view class="icon">
          <image src="../../static/images/weixin.png"></image>
        </view>
        <view class="flex-bd">绑定微信</view>
        <u-icon name="arrow-right" :size="14" color="#999"></u-icon>
      </view>
      <view class="i flex" @click="logout()">
        <view class="icon">
          <image src="../../static/images/tuichu.png"></image>
        </view>
        <view class="flex-bd">退出登录</view>
        <u-icon name="arrow-right" :size="14" color="#999"></u-icon>
      </view>
    </view>
    <!-- 添加u-tabbar -->
    <u-tabbar v-if="UserInfo.UserType == 1" :value="current" :fixed="true" @change="tabbarChange" :activeColor="$c.color()" :placeholder="true" :safeAreaInsetBottom="true">
      <u-tabbar-item text="首页" icon="home"></u-tabbar-item>
      <u-tabbar-item text="我的" icon="account"></u-tabbar-item>
    </u-tabbar>
    <u-tabbar v-else :value="current" :fixed="true" @change="tabbarChange" :activeColor="$c.color()" :placeholder="true" :safeAreaInsetBottom="true">
      <u-tabbar-item text="首页" icon="home"></u-tabbar-item>
      <u-tabbar-item text="通讯录" icon="phone"></u-tabbar-item>
      <u-tabbar-item text="我的" icon="account"></u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
export default {
  data() {
    return {
      current: 0, // 当前选中的tabbar索引
      UserInfo: "",
    };
  },
  onLoad() {
    uni.hideTabBar();
  },
  onShow() {
    this.current = uni.getStorageSync("UserInfo").UserType == 1 ? 1 : 2;
    this.UserInfo = uni.getStorageSync("UserInfo");
  },
  methods: {
    auth() {
      if (this.UserInfo.IsCheck == 2) {
        this.$u.toast("已认证");
      } else {
        this.$c.naviTo("../auth/auth");
      }
    },
    tabbarChange(index) {
      var tabList = [];
      if (this.UserInfo.UserType == 1) {
        tabList = ["../yzHome/yzHome", "../user/user"];
      } else {
        tabList = ["../index/index", "../contacts/contacts", "../user/user"];
      }
      uni.switchTab({
        url: tabList[index],
      });
    },
    logout() {
      uni.setStorageSync("UserInfo", "");
      uni.setStorageSync("UserCode", "");
      uni.setStorageSync("UserType", "");
      uni.navigateTo({
        url: "../login/login",
      });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
